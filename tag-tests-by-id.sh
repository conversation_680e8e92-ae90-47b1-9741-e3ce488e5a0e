#!/bin/bash

#run commands in terminal to run the file
# chmod +x tag-tests-by-id.sh
# ./tag-tests-by-id.sh

TEST_IDS=(
"NE-TC-1076"
"NE-TC-1097"
"NE-TC-1099"
"NE-TC-1240"
"NE-TC-1241"
"NE-TC-1244"
"NE-TC-1245"
"NE-TC-1246"
"NE-TC-1247"
"NE-TC-1248"
"NE-TC-1249"
"NE-TC-1872"
"NE-TC-1873"
"NE-TC-2031"
"NE-TC-2046"
"NE-TC-2388"
"NE-TC-2434"
"NE-TC-2455"
"NE-TC-2456"
"NE-TC-2488"
"NE-TC-2612"
"NE-TC-2667"
"NE-TC-2706"
"NE-TC-3239"
"NE-TC-3403"
"NE-TC-3404"
"NE-TC-3405"

)

NEW_TAG="@Failed"
BASE_FOLDER="cypress/e2e"
FOUND_IDS=()
PROCESSED_FILES=0
UPDATED_FILES=0
SKIPPED_FILES=0
NOT_FOUND_IDS=0

echo "🔍 Starting tagging operation for ${#TEST_IDS[@]} test IDs..."

# Build a simple grep pattern for all test IDs
PATTERN=""
for ID in "${TEST_IDS[@]}"; do
  if [ -z "$PATTERN" ]; then
    PATTERN="$ID"
  else
    PATTERN="$PATTERN|$ID"
  fi
done

# Find files that contain any of the test IDs
FILES_TO_PROCESS=$(find "$BASE_FOLDER" -type f \( -name "*.cy.js" -o -name "*.cy.ts" \) -exec grep -lE "$PATTERN" {} \; 2>/dev/null || true)

if [ -z "$FILES_TO_PROCESS" ]; then
  echo "ℹ️  No files found with the specified test IDs"
else
  echo "📁 Found $(echo "$FILES_TO_PROCESS" | wc -l | tr -d ' ') files to process"
  
  # Create temporary files to store results
  TEMP_RESULTS=$(mktemp)
  TEMP_FOUND_IDS=$(mktemp)
  
  # Process files with threading using Python
  python3 -c "
import re
import sys
import threading
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

NEW_TAG = '$NEW_TAG'
TEST_IDS = [$(printf "'%s'," "${TEST_IDS[@]}" | sed 's/,$//')]

def process_file(file_path):
    try:
        # Read the file
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check which test IDs exist in the file (do this first, regardless of tag status)
        found_ids = []
        for test_id in TEST_IDS:
            if re.search(rf'\\b{test_id}\\b', content):
                found_ids.append(test_id)
        
        # Check if the new tag already exists
        if '\"$NEW_TAG\"' in content or \"'$NEW_TAG'\" in content:
            return file_path, 'SKIP', 'Tag already exists', found_ids
        
        if not found_ids:
            return file_path, 'SKIP', 'No test ID found', []
        
        # Pattern to match existing tags - more comprehensive
        pattern = r'tags:\s*(\[[^\]]+\]|[\'\"@][^,}]+[\'\"]*)'
        
        def replace_tags(match):
            existing_tag = match.group(1)
            
            # If it's already an array, add the new tag to the existing array
            if existing_tag.startswith('['):
                # Extract existing tags from array
                existing_tags = re.findall(r'[\'\"]([^\'\"]+)[\'\"]', existing_tag)
                # Add new tag and create proper array
                existing_tags.append('$NEW_TAG')
                return f'tags: [\"{'\", \"'.join(existing_tags)}\"]'
            else:
                # Single tag, convert to array with both tags
                existing_tag_clean = existing_tag.strip('\'\"')
                return f'tags: [\"$NEW_TAG\", \"{existing_tag_clean}\"]'
        
        # Replace the tags
        new_content = re.sub(pattern, replace_tags, content)
        
        # Write back to file
        with open(file_path, 'w') as f:
            f.write(new_content)
        
        return file_path, 'UPDATED', 'Successfully updated', found_ids
        
    except Exception as e:
        return file_path, 'ERROR', str(e), []

# Get list of files to process
files_to_process = '''$FILES_TO_PROCESS'''.strip().split('\n') if '''$FILES_TO_PROCESS'''.strip() else []

if not files_to_process:
    print('No files to process')
    sys.exit(0)

# Process files with threading
max_workers = min(8, len(files_to_process))  # Use up to 8 threads
results = []
all_found_ids = []

with ThreadPoolExecutor(max_workers=max_workers) as executor:
    # Submit all tasks
    future_to_file = {executor.submit(process_file, file_path): file_path for file_path in files_to_process if file_path.strip()}
    
    # Process completed tasks
    for future in as_completed(future_to_file):
        file_path, status, message, found_ids = future.result()
        results.append((file_path, status, message))
        all_found_ids.extend(found_ids)

# Write results to temporary file
with open('$TEMP_RESULTS', 'w') as f:
    for file_path, status, message in results:
        f.write(f'{file_path}|{status}|{message}\\n')

# Write found IDs to temporary file
with open('$TEMP_FOUND_IDS', 'w') as f:
    for found_id in all_found_ids:
        f.write(f'{found_id}\\n')

print(f'Processed {len(results)} files with {max_workers} threads')
" 2>/dev/null

  # Process results
  if [ -f "$TEMP_RESULTS" ]; then
    while IFS='|' read -r FILE STATUS MESSAGE; do
      if [ -n "$FILE" ]; then
        PROCESSED_FILES=$((PROCESSED_FILES + 1))
        
        case "$STATUS" in
          "UPDATED")
            echo "✅ Updated tags in: $FILE"
            UPDATED_FILES=$((UPDATED_FILES + 1))
            ;;
          "SKIP")
            echo "ℹ️  $MESSAGE: $FILE"
            SKIPPED_FILES=$((SKIPPED_FILES + 1))
            ;;
          "ERROR")
            echo "❌ Error processing $FILE: $MESSAGE"
            ;;
        esac
      fi
    done < "$TEMP_RESULTS"
    
    # Read found IDs from temporary file
    if [ -f "$TEMP_FOUND_IDS" ]; then
      while IFS= read -r ID; do
        if [ -n "$ID" ]; then
          FOUND_IDS+=("$ID")
        fi
      done < "$TEMP_FOUND_IDS"
    fi
    
    # Clean up temporary files
    rm -f "$TEMP_RESULTS" "$TEMP_FOUND_IDS"
  fi
fi

# Count not found test IDs
for ID in "${TEST_IDS[@]}"; do
  FOUND=false
  for FOUND_ID in "${FOUND_IDS[@]}"; do
    if [ "$ID" = "$FOUND_ID" ]; then
      FOUND=true
      break
    fi
  done
  if [ "$FOUND" = false ]; then
    echo "❌ Test case not found: $ID"
    NOT_FOUND_IDS=$((NOT_FOUND_IDS + 1))
  fi
done

echo ""
echo "🎯 Tagging operation complete!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📊 Summary:"
echo "   • Total test IDs: ${#TEST_IDS[@]}"
echo "   • Files processed: $PROCESSED_FILES"
echo "   • Files updated: $UPDATED_FILES"
echo "   • Files skipped (tag exists): $SKIPPED_FILES"
echo "   • Test IDs not found: $NOT_FOUND_IDS"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
