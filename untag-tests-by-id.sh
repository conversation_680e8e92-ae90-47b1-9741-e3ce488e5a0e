#!/bin/bash

#run commands in terminal to run the file
# chmod +x untag-tests-by-id.sh
# ./untag-tests-by-id.sh

TEST_IDS=(
"NE-TC-1076"
"NE-TC-1097"
"NE-TC-1099"
"NE-TC-1240"
"NE-TC-1241"
"NE-TC-1244"
"NE-TC-1245"
"NE-TC-1246"
"NE-TC-1247"
"NE-TC-1248"
"NE-TC-1249"
"NE-TC-1250"
"NE-TC-1251"
"NE-TC-1252"
"NE-TC-1253"
"NE-TC-1254"
"NE-TC-1255"
"NE-TC-1256"
"NE-TC-1257"
"NE-TC-1872"
"NE-TC-1873"
"NE-TC-2031"
"NE-TC-2032"
"NE-TC-2046"
"NE-TC-2388"
"NE-TC-2434"
"NE-TC-2455"
"NE-TC-2456"
"NE-TC-2488"
"NE-TC-2612"
"NE-TC-2667"
"NE-TC-2706"
"NE-TC-3239"
"NE-TC-3403"
"NE-TC-3404"
"NE-TC-3405"
)

TAG_TO_REMOVE="@Failed"
BASE_FOLDER="cypress/e2e"
FOUND_IDS=()
PROCESSED_FILES=0
UPDATED_FILES=0
SKIPPED_FILES=0
NOT_FOUND_IDS=0

echo "🔍 Starting untagging operation for ${#TEST_IDS[@]} test IDs..."

# Build a simple grep pattern for all test IDs
PATTERN=""
for ID in "${TEST_IDS[@]}"; do
  if [ -z "$PATTERN" ]; then
    PATTERN="$ID"
  else
    PATTERN="$PATTERN|$ID"
  fi
done

# Find files that contain any of the test IDs AND the tag to remove
FILES_TO_PROCESS=$(find "$BASE_FOLDER" -type f \( -name "*.cy.js" -o -name "*.cy.ts" \) -exec grep -lE "$PATTERN" {} \; | xargs grep -lE "\"$TAG_TO_REMOVE\"|'$TAG_TO_REMOVE'" 2>/dev/null || true)

if [ -z "$FILES_TO_PROCESS" ]; then
  echo "ℹ️  No files found with both test IDs and the tag to remove"
else
  echo "📁 Found $(echo "$FILES_TO_PROCESS" | wc -l | tr -d ' ') files to process"
  
  # Process each file
  while IFS= read -r FILE; do
    if [ -n "$FILE" ]; then
      PROCESSED_FILES=$((PROCESSED_FILES + 1))
      
      # Track which test IDs are found in this file
      for ID in "${TEST_IDS[@]}"; do
        if grep -qE "\b${ID}\b" "$FILE"; then
          FOUND_IDS+=("$ID")
        fi
      done
      
      # Use Python for more robust tag handling
      python3 -c "
import re
import sys

# Read the file
with open('$FILE', 'r') as f:
    content = f.read()

# Pattern to match existing tags
pattern = r'tags:\s*(\[[^\]]+\]|[\'\"@][^,}]+[\'\"]*)'

def remove_tag(match):
    existing_tag = match.group(1)
    
    # If it's an array, remove the specific tag
    if existing_tag.startswith('['):
        # Extract existing tags from array
        existing_tags = re.findall(r'[\'\"]([^\'\"]+)[\'\"]', existing_tag)
        # Remove the tag to remove
        filtered_tags = [tag for tag in existing_tags if tag != '$TAG_TO_REMOVE']
        
        if len(filtered_tags) == 0:
            # No tags left, remove the entire tags line
            return 'SKIP_LINE'
        elif len(filtered_tags) == 1:
            # Single tag left, convert back to single tag format
            return f'tags: \"{filtered_tags[0]}\"'
        else:
            # Multiple tags left, keep as array
            return f'tags: [\"{'\", \"'.join(filtered_tags)}\"]'
    else:
        # Single tag, check if it's the one to remove
        existing_tag_clean = existing_tag.strip('\'\"')
        if existing_tag_clean == '$TAG_TO_REMOVE':
            return 'SKIP_LINE'
        else:
            return match.group(0)  # Keep as is

# Replace the tags
new_content = re.sub(pattern, remove_tag, content)

# Remove lines that were marked for skipping
lines = new_content.split('\n')
filtered_lines = []
for line in lines:
    if 'SKIP_LINE' not in line:
        filtered_lines.append(line)
new_content = '\n'.join(filtered_lines)

# Write back to file
with open('$FILE', 'w') as f:
    f.write(new_content)

print('UPDATED')
" 2>/dev/null

      echo "✅ Removed tag from: $FILE"
      UPDATED_FILES=$((UPDATED_FILES + 1))
    fi
  done <<< "$FILES_TO_PROCESS"
fi

# Find files that contain test IDs but don't have the tag (for skipped count)
FILES_WITH_IDS=$(find "$BASE_FOLDER" -type f \( -name "*.cy.js" -o -name "*.cy.ts" \) -exec grep -lE "$PATTERN" {} \; 2>/dev/null || true)
FILES_WITH_TAG=$(find "$BASE_FOLDER" -type f \( -name "*.cy.js" -o -name "*.cy.ts" \) -exec grep -lE "\"$TAG_TO_REMOVE\"|'$TAG_TO_REMOVE'" {} \; 2>/dev/null || true)

# Count files that have test IDs but no tag
SKIPPED_FILES=$(comm -23 <(echo "$FILES_WITH_IDS" | sort) <(echo "$FILES_WITH_TAG" | sort) | wc -l | tr -d ' ')

# Count not found test IDs
for ID in "${TEST_IDS[@]}"; do
  FOUND=false
  for FOUND_ID in "${FOUND_IDS[@]}"; do
    if [ "$ID" = "$FOUND_ID" ]; then
      FOUND=true
      break
    fi
  done
  if [ "$FOUND" = false ]; then
    echo "❌ Test case not found: $ID"
    NOT_FOUND_IDS=$((NOT_FOUND_IDS + 1))
  fi
done

echo ""
echo "🎯 Untagging operation complete!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📊 Summary:"
echo "   • Total test IDs: ${#TEST_IDS[@]}"
echo "   • Files processed: $PROCESSED_FILES"
echo "   • Files updated: $UPDATED_FILES"
echo "   • Files skipped (no tag): $SKIPPED_FILES"
echo "   • Test IDs not found: $NOT_FOUND_IDS"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" 