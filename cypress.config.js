require('dotenv').config();
const { defineConfig } = require('cypress');
const { user, host, database, password } = require('pg/lib/defaults');
const pg = require('pg');
const { registerAIOTestsPlugin } = require('cypress-aiotests-reporter/src');
const fs = require('fs');
const { convertXlsxToJson } = require('./cypress/support/xlsxUtils.js');
const { downloadFile } = require('cypress-downloadfile/lib/addPlugin');
const pdf = require('pdf-parse');
const path = require('path');
const { collectFailingTests } = require('cypress-plugin-last-failed');

const DB = {
  user: process.env.CYPRESS_DB_USER,
  host: process.env.CYPRESS_DB_HOST,
  database: process.env.CYPRESS_DB_DATABASE,
  password: process.env.CYPRESS_DB_PASSWORD,
  port: '5432',
  max: 10
};

const pool = new pg.Pool(DB);

module.exports = defineConfig({
  projectId: 'rpnuq8',
  chromeWebSecurity: false,
  pageLoadTimeout: 120000,
  defaultCommandTimeout: 20000,
  taskTimeout: 80000,
  requestTimeout: 120000,
  responseTimeout: 120000,
  viewportWidth: 1920,
  viewportHeight: 1080,
  adminUsername: '<EMAIL>',
  adminPassword: 'Nivoda123',
  experimentalMemoryManagement: true,
  numTestsKeptInMemory: 2,
  experimentalModifyObstructiveThirdPartyCode: true,

  env: {
    apiurl: process.env.CYPRESS_API_URL,
    loginApiUrl: process.env.CYPRESS_API_Login_URL,
    translationApiUrl: process.env.CYPRESS_TRANSLATION_API_URL,
    adminApiUrl: process.env.CYPRESS_ADMIN_API_URL,
    supplierApiUrl: process.env.CYPRESS_SUPPLIER_API_URL,
    spaceCodeApiUrl: process.env.CYPRESS_SPACE_CODE_URL,
    jewelleryUploadApi: process.env.CYPRESS_Jewellery_Upload_Api,
    showRoomUrl: process.env.CYPRESS_Showroom_URL,
    showRoomApiUrl: process.env.CYPRESS_Showroom_API_URL,
    grepFilterSpecs: true,
    grepOmitFiltered: true,
    customerPassword: 'Nivoda123',

    aioTests: {
      enableReporting: true,
      cloud: {
        apiKey: 'ODY5YmM2OTEtNTYwNy0zNWM5LTlhYzMtZDg1MWY1OTlkYTNmLmM3YzQ1NzVhLWQ2MTYtNDYzMi1iY2UzLThhZTFmM2U1NDg0MA=='
      },
      jiraProjectId: '10006',
      cycleDetails: {
        cycleKey: process.env.CYCLE_KEY
      },
      addNewRun: true,
      addAttachmentToFailedCases: true,
      createNewRunForRetries: true,
      addTestBodyToComments: true
    }
  },

  e2e: {
    experimentalRunAllSpecs: true,
    specPattern: ['cypress/e2e/1-Db/**/*.cy.js', 'cypress/e2e/Product/**/*.cy.js', 'cypress/e2e/**/*.cy.js'],

    setupNodeEvents(on, config) {
      registerAIOTestsPlugin(on, config);
      collectFailingTests(on, config);

      require('@bahmutov/cy-grep/src/plugin')(config);

      on('task', {
        downloadFile
      });

      on('task', {
        convertXlsxToJson(filePath) {
          return convertXlsxToJson(filePath);
        },
        readPdf(pdfPath) {
          return new Promise((resolve) => {
            const filePath = path.resolve(pdfPath);
            const dataBuffer = fs.readFileSync(filePath);
            pdf(dataBuffer).then((data) => {
              resolve(data.text);
            });
          });
        },
        READFROMDB({ sql }) {
          return pool.query(sql).then((result) => {
            return result;
          });
        },

        getDownloadedFileName() {
          const downloadsFolder = config.downloadsFolder || 'cypress/downloads';
          if (!fs.existsSync(downloadsFolder)) {
            throw new Error(`Downloads folder does not exist: ${downloadsFolder}`);
          }
          const files = fs.readdirSync(downloadsFolder);
          if (files.length === 0) {
            throw new Error('No files found in the downloads folder.');
          }
          return files[0];
        },
        writeToFile({ filename, data }) {
          fs.writeFileSync(filename, JSON.stringify(data, null, 2), 'utf-8');
          return null;
        }
      });
      on('after:run', () => {
        return pool.end().then(() => {
          console.log('Database connection pool closed (after:run).');
        });
      });

      return config;
    },
    retries: {
      runMode: 0,
      openMode: 0
    },
    baseUrl: process.env.CYPRESS_BASE_URL
  }
});
