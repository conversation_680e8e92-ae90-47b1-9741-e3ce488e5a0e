FROM cypress/included:14.4.0

# Set required env variables
ENV CYPRESS_CACHE_FOLDER=/root/.cache/Cypress \
    ELECTRON_EXTRA_LAUNCH_ARGS="--disable-dev-shm-usage --no-sandbox --js-flags=--max-old-space-size=8192" \
    NODE_OPTIONS="--max-old-space-size=8192" \
    CYPRESS_numTestsKeptInMemory=1

WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci --silent

COPY . .
RUN mkdir -p cypress/results && \
    npm run cy:db:run || echo "DB init skipped"

# Optional: Backup node_modules if needed by init container
RUN mkdir -p /opt/node_modules_cache && cp -r node_modules /opt/node_modules_cache || true


