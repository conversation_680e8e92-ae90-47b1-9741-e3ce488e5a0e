apiVersion: batch/v1
kind: Job
metadata:
  name: cypress-parallel-tests-machine-1
  namespace: automation
spec:
  completions: NO_OF_MACHINES
  parallelism: NO_OF_MACHINES
  completionMode: Indexed
  template:
    metadata:
      labels:
        app: cypress-test
    spec:
      restartPolicy: Never
      volumes:
        - name: dshm
          emptyDir:
            medium: Memory
        - name: cypress-cache
          emptyDir: {}
        - name: node-modules
          emptyDir: {}

      initContainers:
        - name: warm-cache
          image: IMAGE_REPO:latest
          command: ['/bin/sh', '-c']
          args:
            - |
              echo "Warming node_modules and Cypress cache..."
              mkdir -p /tmp/node_modules
              cp -r /opt/node_modules_cache/* /tmp/node_modules/ || true

              mkdir -p /cypress-cache
              cp -r /root/.cache/Cypress/* /cypress-cache/ || true

          volumeMounts:
            - name: node-modules
              mountPath: /tmp/node_modules
            - name: cypress-cache
              mountPath: /cypress-cache

      containers:
        - name: cypress
          image: IMAGE_REPO:latest
          resources:
            requests:
              memory: '12Gi'
              cpu: '3000m'
            limits:
              memory: '12Gi'
              cpu: '3000m'

          env:
            - name: CYCLE_KEY
              value: CYCLE_KEY_VALUE
            - name: JOB_INDEX
              valueFrom:
                fieldRef:
                  fieldPath: metadata.annotations['batch.kubernetes.io/job-completion-index']
            - name: CYPRESS_PARALLEL_TOTAL
              value: 'NO_OF_MACHINES'
            - name: CYPRESS_CACHE_FOLDER
              value: '/root/.cache/Cypress'
            - name: NODE_OPTIONS
              value: '--max-old-space-size=8192'

          command: ['/bin/sh', '-c']
          args:
            - |
              echo "Preparing test run for machine $JOB_INDEX of $CYPRESS_PARALLEL_TOTAL"

              # Restore node_modules if warm cache exists
              if [ -d "/tmp/node_modules" ] && [ "$(ls -A /tmp/node_modules)" ]; then
                echo "Using pre-warmed node_modules"
                mkdir -p /app/node_modules
                cp -r /tmp/node_modules/* /app/node_modules/ || true
              fi

              # Restore Cypress cache
              if [ -d "/cypress-cache" ] && [ "$(ls -A /cypress-cache)" ]; then
                echo "Using pre-warmed Cypress cache"
                mkdir -p /root/.cache/Cypress
                cp -r /cypress-cache/* /root/.cache/Cypress/ || true
              fi

              npx cypress verify
              npx cypress info

              export CYPRESS_PARALLEL_INDEX=$JOB_INDEX

              CYPRESS_RECORD_KEY=5b6adac1-9a6e-483e-ab47-7d386666ff92 npx cypress run \
                --env grepTags=@TAG_COMPONENT,grepFilterSpecs=true \
                --record \
                --parallel \
                --group TAG_COMPONENT \
                --browser electron \
                --ci-build-id CI_BUILD_ID_PLACEHOLDER \
                --tag TAG_COMPONENT

          volumeMounts:
            - name: dshm
              mountPath: /dev/shm
            - name: cypress-cache
              mountPath: /root/.cache/Cypress
            - name: node-modules
              mountPath: /tmp/node_modules

  backoffLimit: 2
