/// <reference types="cypress" />
class Showroom {
  constructor() {
    // Locators

    this.browserSelection = '[data-automation-id="browse-selections-button"]';
    this.findthePerfectStone = '[data-automation-id="walkin-button"]';
    this.searchBar = '[data-automation-id="search-box"]';
    this.labgrownNavBar = '.rt-BaseTabListTriggerInnerHidden.rt-TabNavLinkInnerHidden';
    this.title = '[data-automation-id="diamond-link-title"]';
    this.container = '.sc-hzNEM.icDCbq';
    this.shortList = '[data-automation-id="add_shortlist"]';
    this.removeFromShortList = '[data-automation-id="remove_shortlist"]';
    this.addToBasketButton = '[data-automation-id="add-to-cart"';
    this.addToBasketcommentBox = '[data-automation-id="customer-reference-textarea"]';
    this.toastMessage = '.sc-frDJqD';
    this.viewButton = '[data-automation-id="toast-notification-btn"]';
    this.viewCart = '';
    this.homeFloatingMenu = '[data-automation-id="nav-item-home"]';
    this.browseFloatingMenu = '[data-automation-id="nav-item-browse""]';
    this.shortlistFloatingMenu = '[data-automation-id="nav-item-shortlist"]';
    this.basketFloatingMenu = '[data-automation-id="nav-item-basket"]';
    this.placeOrderRequest = '[data-automation-id="place-order-btn"]';
    this.confirmAndPlaceOrderRequest = '[data-automation-id="confirm-order-modal-btn-code-enabled"]';
    this.addtoBasketPopUpBox = '.sc-hmXxxW.eEMmoU';
    this.floadtingMenu = '.sc-fAMDQA.fFRLyJ';
    this.orderConfirmationBox = '.sc-eOnLuU.iQQBg';
    this.confirmButton = '.rt-BaseButton';
    this.popupDiamondImage = 'img[alt="Diamond"]';
    this.popupTitle = 'span.sc-kUaPvJ';
    this.popupCertificateNo = 'span.sc-VJcYb';
    this.popupDiamondDetails = 'span.sc-VJcYb';
    this.popupPrice = '.price';
    this.showRoomCheckOutSettingBox = '.rt-Box';
    this.changeCheckOutPopUp = '.rt-Text';
    this.closeIcon = '.rt-BaseButton';
    this.switchSettingConfirmButton = '[data-automation-id="checkout-modal-action-switch-checkout-settings-confirm"]';
    this.switchSettingCancelButton = '[data-automation-id="checkout-modal-action-switch-checkout-settings-cancel"]';
    this.removeCartPopUp = '.sc-csSMhA.dfXoPc';
    this.outOfStockPopUp = '.sc-jGDUUe.kQselR';
    this.removeItemsFromCart = '[data-automation-id="checkout-modal-action-confirm-switch-checkout-confirm"]';
    this.removeItemsFromCartCancelButton = '[data-automation-id="checkout-modal-action-confirm-switch-checkout-cancel"';
    this.addToBasketNivodaCartConfirmButton = '[data-automation-id="add-to-basket-confirm-btn"]';
    this.otpCodeField = '.pin_modal';
    this.otpInputField = 'input[id="';
    this.confirmShowroomPlaceOrderButton = '[data-automation-id="confirm-order-modal-btn"]';
  }

  accessDashboard() {
    cy.get(this.browserSelection).contains('Browse selection').click();
    cy.url().should('include', '/live/result/diamond/natural?');
  }
  accessPerfectStoneWizard() {
    cy.get(this.findthePerfectStone).contains('Find the perfect stone').click();
    cy.url().should('include', 'live/walk-in');
  }
  visitNaturalDiamonds() {
    const apiUrl = Cypress.env('showRoomApiUrl');
    cy.intercept('POST', apiUrl, (req) => {
      if (req.body.operationName === 'NaturalDiamonds') {
        req.alias = 'getproduct';
      }
    });

    cy.containsTranslation('natural_diamonds', { withinSubject: $nav }).click();
    cy.wait('@getproduct').then((interception) => {
      expect(interception.response.statusCode).to.eq(200);
    });

    cy.url().should('include', '/live/result/diamond/natural?');
  }

  visitLabGrownDiamonds() {
    const apiUrl = Cypress.env('showRoomApiUrl');
    cy.intercept('POST', apiUrl, (req) => {
      if (req.body.operationName === 'LabgrownDiamond') {
        req.alias = 'getproduct';
      }
    });

    cy.get(this.labgrownNavBar, { timeout: 60000 })
      .eq(1)
      .then(($nav) => {
        cy.containsTranslation('labgrown', { withinSubject: $nav }, { timeout: 60000 }).click({ force: true });
      });

    cy.wait('@getproduct').then((interception) => {
      expect(interception.response.statusCode).to.eq(200);
    });

    cy.url().should('include', '/live/result/diamond/labgrown?');
  }

  searchStone(fixtureFileName) {
    return cy.readFixtureFile(fixtureFileName).then((certificateData) => {
      const certificateno = certificateData[0].certNumber;
      const apiUrl = Cypress.env('showRoomApiUrl');
      cy.intercept('POST', apiUrl, (req) => {
        const query = req.body.query || '';
        if (query.includes('diamonds_by_query')) {
          req.alias = 'getproduct';
        }
      });

      cy.get(this.searchBar).clear({ force: true }).click({ force: true }).type(certificateno);

      return cy.wait('@getproduct').then((interception) => {
        expect(interception.response.statusCode).to.eq(200);
        console.log('Search Stone Certificate', certificateno);
        return certificateno;
      });
    });
  }
  verifyDiamondText(stoneType, StoneText) {
    cy.contains('span', stoneType, { timeout: 20000 }).should('contain.text', StoneText);
  }
  addProductToCart(fixtureFileName, stoneType, StoneText) {
    this.searchStone(fixtureFileName).then(() => {
      this.verifyDiamondText(stoneType, StoneText);
    });
  }
  addProductToShowRoomCart(fixtureFileName, stoneType, StoneText) {
    this.addProductToCart(fixtureFileName, stoneType, StoneText);
    cy.get(this.addToBasketButton).click();
    cy.get(this.addToBasketButton, { timeout: 6000 }).contains('Remove');
  }
  placeShowroomOrder() {
    cy.get(this.basketFloatingMenu).click();
    cy.get(this.confirmAndPlaceOrderRequest).click();
    cy.getOTP(new Date()).then((otpCode) => {
      const otpDigits = otpCode.split('');
      cy.get(this.otpCodeField, { timeout: 150000 }).should('be.visible');
      for (let i = 0; i < otpDigits.length; i++) {
        cy.get(`${this.otpInputField}${i}"]`, { timeout: 150000 })
          .should('be.visible')
          .clear()
          .type(otpDigits[i], { force: true });
      }
      cy.get(this.confirmShowroomPlaceOrderButton).click();
    });
  }
  addProductToNivodaCartSection(index) {
    cy.get(this.addToBasketButton).click();
    cy.get(this.addToBasketcommentBox).type('Product To Nivoda Cart From Showroom');
    cy.get(this.addToBasketNivodaCartConfirmButton).contains('Confirm').click();
    cy.get(this.toastMessage).contains('Added to your basket');
    cy.get(this.addToBasketButton, { timeout: 6000 }).contains('Added');
    cy.get(this.basketFloatingMenu).click();
    cy.get(this.placeOrderRequest).click();
    cy.get(this.confirmAndPlaceOrderRequest).click();
    cy.get(this.orderConfirmationBox).should('have.text', 'Order request placed');
  }
  addProductToNivodaCartAndPlaceOrder(fixtureFileName, stoneType, StoneText, index) {
    this.addProductToCart(fixtureFileName, stoneType, StoneText);
    this.addProductToNivodaCartSection(index);
  }
  addProductFromPDPToNivodaCart(fixtureFileName, stoneType, StoneText, index) {
    this.addProductToCart(fixtureFileName, stoneType, StoneText);
    cy.get(this.title).click();
    this.addProductToNivodaCartSection(index);
  }
  addProductFromShortListToNivodaCart(fixtureFileName, stoneType, StoneText, index) {
    this.addProductToCart(fixtureFileName, stoneType, StoneText);
    cy.get(this.shortList).click();
    cy.wait(3000);
    cy.get(this.shortlistFloatingMenu).click();
    this.addProductToNivodaCartSection(index);
  }
  changeCheckoutSetting() {
    cy.get(this.closeIcon).eq(1).click({ force: true });
    cy.get(this.showRoomCheckOutSettingBox).eq(1).contains('Add items to your Nivoda cart');
    cy.get(this.showRoomCheckOutSettingBox).eq(0).contains('Create orders from Showroom').click();

    cy.get(this.changeCheckOutPopUp, { force: true }).contains('Switch checkout settings?');
    cy.get(this.confirmButton).contains('Confirm').click();
    cy.get(this.changeCheckOutPopUp).contains(
      'Changing the checkout mode will clear all items in your current cart. This action cannot be undone.'
    );
  }
  verifyBasketCount() {
    cy.get(this.addToBasketButton).click();
    cy.get(this.addToBasketcommentBox).type('Product To Nivoda Cart From Showroom');
    cy.get(this.browserSelection).contains('Confirm').click();
    cy.get(this.toastMessage).contains('Added to your basket');
    cy.get(this.floadtingMenu).contains('Basket').parent().find('.rt-Text.sc-lgsldV.ghIYrD').should('have.text', '1');
  }
  verifyBasketCountOnCheckoutChange() {
    cy.get(this.removeItemsFromCart).contains('Remove items from cart').click();
    cy.get(this.basketFloatingMenu).contains('Basket').parent().find('.rt-Text.sc-lgsldV.ghIYrD').should('not.exist');
  }
  verifyAddToNivodaCartPopUpContents() {
    cy.get(this.addToBasketButton).click();

    cy.get(this.addtoBasketPopUpBox).within(() => {
      cy.get(this.popupDiamondImage).should('be.visible');
      cy.get(this.popupTitle).should('be.visible');
      cy.get(this.popupCertificateNo).should('be.visible');
      cy.get(this.popupDiamondDetails).should('be.visible');
      cy.get(this.popupPrice).should('be.visible');
    });
  }
  verifyViewBasketButton() {
    cy.get(this.addToBasketButton).click();
    cy.get(this.viewButton).contains('View').click();
    cy.url().should('include', 'live/order-checkout');
  }
  verifyOutOfStockStonePopup() {
    cy.get(this.placeOrderRequest).contains('Place order request').click();
    cy.get(this.confirmAndPlaceOrderRequest).contains('Confirm and place request').click();
    cy.get(this.outOfStockPopUp).should('have.text', 'The following item is out of stock');
  }
  verifyAddToShortList(fixtureFileName, stoneType, StoneText) {
    this.addProductToCart(fixtureFileName, stoneType, StoneText);
    cy.get(this.shortList).eq(0).click();
  }
}
export default Showroom;
