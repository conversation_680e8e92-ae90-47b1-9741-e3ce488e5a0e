/// <reference types="cypress" />
const path = require('path');
import <PERSON> from 'papapar<PERSON>';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat);
const { uniqueNamesGenerator, names } = require('unique-names-generator');
const {
  UniqueString,
  UniqueNumber,
  UniqueStringId,
  UniqueNumberId,
  UniqueOTP,
  UniqueCharOTP,
  HEXColor,
  uuid
} = require('unique-string-generator');
const firstName = uniqueNamesGenerator({ dictionaries: [names] });
const lastName = uniqueNamesGenerator({ dictionaries: [names] });

class Supplier {
  constructor() {
    this.downloadStock = '[data-automation-id="download-stock"]';
    this.removeStockButton = '[data-automation-id="remove-stock"]';
    this.removeStockTitle = '[data-automation-id="remove-stock-title"]';
    this.removeInventoryText =
      '.MuiTypography-root.MuiDialogContentText-root.MuiTypography-body1.MuiDialogContentText-root.css-p9wg1u';
    this.cancelButton = '[data-automation-id="cancel-button"]';
    this.removeMyStockButton = '[data-automation-id="remove-my-stock"]';
    this.input = 'input[placeholder="Enter \'Remove\'"]';
    this.popup = '.MuiSnackbar-message.MuiSnackbar-message > .MuiTypography-root';
    this.upload = '[data-testid="dropzone"] input[type="file"]';
    this.uploadButton = 'button[type="button"]';
    this.viewStockButton = '[data-automation-id="view-my-stock-button"]';
    this.addYourFileText = '[data-automation-id="uploaded-file-name"]';
    this.listViewBtn = '.MuiGrid-root > .MuiButtonBase-root';
    this.gridViewBtn =
      '.MuiButtonBase-root.MuiIconButton-root.MuiIconButton-colorSecondary.MuiIconButton-sizeMedium.css-zmdk6q';
    this.gridResult = '.MuiPaper-root.MuiPaper-outlined.MuiPaper-rounded.MuiCard-root.css-ipnx9d';
    this.listResult = '.MuiTableRow-root.css-6exm6i';
    this.supplierUploadStockNav = '[data-testid="tab-item"]';
    this.time = '.MuiBox-root.css-1dbbfsm';
    this.status = '.MuiTableCell-sizeMedium.css-jn8x2i';
    this.viewDetailsButton = '.MuiDialogActions-root > .MuiBox-root > :nth-child(2)';
    this.uploadStockDropDown = '[data-automation-id="select-stone-type-select-input"]';
    this.invalidStone = '.MuiTableHead-root > .MuiTableRow-root > :nth-child(1)';
    this.loading = '.fade-in';
    this.table = "table[class='MuiTable-root css-1uvsxdf'] tr[class='MuiTableRow-root MuiTableRow-head css-6exm6i']";
    this.uploadHistoryCta = 'a[aria-label="/upload/history"]';
    this.uploadImgCta = 'a[aria-label="/upload/image"]';
    this.profileUploadInput = 'button[type="button"] input[type="file"]';
    this.successMessage = 'p.MuiTypography-root.MuiTypography-body2.MuiSnackbar-title.css-svcbgx';
    this.imgPath = 'cypress/fixtures/profileimage.png';
    this.firstNameInput = 'input[placeholder="First name"][type="text"]';
    this.saveButton = 'button.MuiButton-root';
    this.toggleCheckbox = 'input[type="checkbox"].PrivateSwitchBase-input';
    this.currentPasswordInput = 'input[placeholder="Current password"]';
    this.newPasswordInput = 'input[placeholder="New password"]';
    this.confirmPasswordInput = 'input[placeholder="Confirm password"]';
    this.websiteInput = 'input[placeholder="Website"]';
    this.addUserButton = 'button.MuiButton-containedPrimary';
    this.lastNameInput = '#lastName';
    this.emailInput = '#email';
    this.phoneNumberInput = '#phone';
    this.locationNameInput = '#Location-name';
    this.locationName = '#location';
    this.countryInput = 'input.MuiAutocomplete-input';
    this.stateInput = '#state';
    this.postalCodeInput = '#postalCode';
    this.cityInput = '#city';
    this.addressLineInput = '#addressLine1';
    this.saveChangesButton = '[data-testid="save-changes-btn"]';
    this.locationSuccessMessage = 'p.MuiSnackbar-title';
    this.countryInputSelector = 'input.MuiAutocomplete-input[role="combobox"]';
    this.stateDropdownButton = '#state';
    this.stateDropClick = 'button[aria-label="Open"]';
    this.stateOption = '#state-option-20';
    this.menuButton =
      '[style="display: flex; gap: 16px; justify-content: center; align-items: center;"] > .MuiButtonBase-root';
    this.naturalMenuItemButton =
      ':nth-child(1) > .MuiList-root > :nth-child(1) > .MuiListItem-root > .MuiButtonBase-root';
    this.allMenu = '.css-pkwep5 > :nth-child(1) > :nth-child(1)';
    this.fancyClicked = 'button[value="Fancy"]';
    this.searchNameInput = 'input[placeholder="Enter name"]';
    this.saveButtonText = 'svg.add-playlist-icon';
    this.saveClickAfterText = 'button.MuiButton-containedPrimary';
    this.defaultClicked = '.MuiTypography-root';
    this.sampleArrow = 'div.MuiBox-root';
    this.sortDropDown = '.MuiList-root';
    this.myStockOnlyButton = 'button[value="my-stock"]';
    this.certInput = 'input[name="cert-number-stock-id"]';
    this.certFilter = 'button[value="GIA"]';
    this.shapeFilter = 'p.MuiTypography-root.MuiTypography-body1.css-k5avy7';
    this.advancedCTA = 'button[value="Advanced"]';
    this.caratFilter = '.MuiGrid-grid-xs-8';
    this.cutFilter = 'p.MuiTypography-root';
    this.eyeCleanBgm = 'p.MuiTypography-root.MuiTypography-body1.css-1v4rkw2';
    this.mainMenuFilter = 'p.MuiTypography-root.MuiTypography-body2.css-6ym9xs';
    this.trashIcon = 'svg.icon-trash';
    this.successToast = 'p.MuiSnackbar-title';
    this.cancelBtn = '.MuiDialogActions-root > .MuiButton-text';
    this.ownerDeleteMsg = 'p.MuiDialogContentText-root';
    this.setDefaultStarIcon = 'svg.icon-star.css-iguwhy';
    this.setLocationSuccessMessage = 'p.MuiTypography-body2.MuiSnackbar-title.css-svcbgx';
    this.editIcon = 'svg.icon-edit';
    this.searchInput = 'input[placeholder="Search"]';
    this.searchResult =
      'td.MuiTableCell-root.MuiTableCell-body.MuiTableCell-alignLeft.MuiTableCell-sizeMedium.css-1q92jwr';
    this.noResultMessage = 'p.MuiTypography-root.MuiTypography-body1.css-1kspfux';
    this.bottomTableCount =
      '.MuiTableCell-root.MuiTableCell-body.MuiTableCell-alignCenter.MuiTableCell-sizeMedium.css-jn8x2i';
    this.topTableCount = '.MuiTableCell-root.MuiTableCell-body.MuiTableCell-sizeMedium.css-qqlw3i';
    this.reason = '.MuiTableRow-root.css-6exm6i';
    this.bottomTableCount =
      '.MuiTableCell-root.MuiTableCell-body.MuiTableCell-alignCenter.MuiTableCell-sizeMedium.css-jn8x2i';
    this.back =
      '.MuiButtonBase-root.MuiButton-root.MuiButton-text.MuiButton-textPrimary.MuiButton-sizeMedium.MuiButton-textSizeMedium.MuiButton-colorPrimary.MuiButton-disableElevation.MuiButton-root.MuiButton-text.MuiButton-textPrimary.MuiButton-sizeMedium.MuiButton-textSizeMedium.MuiButton-colorPrimary.MuiButton-disableElevation.css-1aeqobs';
    this.priceRank = 'button:has(svg.icon-download)';
    this.trashIcon = 'svg.icon-trash';
    this.deleteCTA = 'button.MuiButton-containedError';
    this.successToast = 'p.MuiSnackbar-title';
    this.cancelBtn = '.MuiDialogActions-root > .MuiButton-text';
    this.ownerDeleteMsg = 'p.MuiDialogContentText-root';
    this.setDefaultStarIcon = 'svg.icon-star.css-iguwhy';
    this.setLocationSuccessMessage = 'p.MuiTypography-body2.MuiSnackbar-title.css-svcbgx';
    this.editIcon = 'svg.icon-edit';
    this.searchInput = 'input[placeholder="Search"]';
    this.searchResult =
      'td.MuiTableCell-root.MuiTableCell-body.MuiTableCell-alignLeft.MuiTableCell-sizeMedium.css-1q92jwr';
    this.noResultMessage = 'p.MuiTypography-root.MuiTypography-body1.css-1kspfux';
    this.bottomTableCount =
      '.MuiTableCell-root.MuiTableCell-body.MuiTableCell-alignCenter.MuiTableCell-sizeMedium.css-jn8x2i';
    this.locationNameOption = '#location-option-0';
    this.confirmLocationAddedMessage = '[data-automation-id="toast"]';
    this.checkboxSelector = 'input[type="checkbox"].PrivateSwitchBase-input';
    this.deleteIconSelector = 'button[aria-label="Remove"]';
    this.deletionSnackbar = 'p.MuiTypography-root.MuiSnackbar-title';
    this.dropdownToggleButton = 'button[aria-haspopup="true"]';
    this.dropdownOption = 'input[type="checkbox"][aria-label="controlled"]';
    this.applyButton = 'button.MuiButton-root.MuiButton-outlined';
    this.clearButton = 'button.MuiButton-text.MuiButton-textPrimary';
    this.uploadImgClicked = 'button.MuiButton-root[type="button"]';
    this.confirmDeleteButton = '[data-testid="delete-button"]';
  }

  verifyDownloadMyStockButton(fixtureFileName) {
    cy.get(this.downloadStock).eq(1).click();
    cy.get(this.downloadStock).eq(1).should('not.have.text', 'Loading...');
    cy.wait(2000);
    cy.getDownloadedFileName().then((fileName) => {
      const baseName = Cypress._.trimEnd(fileName, path.extname(fileName));
      cy.log('Downloaded file name:', baseName);
      cy.readFixtureFile(fixtureFileName).then((data) => {
        const companyname = data[0].name;
        cy.log(companyname);
        expect(baseName).to.equal(companyname + '-stock-file');
        cy.readFile(Cypress.config('downloadsFolder') + '/' + fileName).then((csvContent) => {
          const parsedCsv = Papa.parse(csvContent, { header: true });
          const headers = parsedCsv.meta.fields;
          const expectedHeaders = [
            'Stock #',
            'Availability',
            'Shape',
            'Weight',
            'Color',
            'Clarity',
            'Cut',
            'Polish',
            'Symmetry',
            'Fluorescence Intensity',
            'Fluorescence Color',
            'Measurements',
            'Shade',
            'Milky',
            'Eye Clean',
            'Lab',
            'Report #',
            'Location',
            'Treatment',
            'Discount',
            'Price Per Carat',
            'Final Price',
            'Depth %',
            'Table %',
            'Girdle Thin',
            'Girdle Thick',
            'Girdle %',
            'Girdle Condition',
            'Culet Size',
            'Crown Height',
            'Crown Angle',
            'Pavilion Depth',
            'Pavilion Angle',
            'Inscription',
            'Cert comment',
            'KeyToSymbols',
            'Fancy Color',
            'Fancy Color Intensity',
            'Fancy Color Overtone',
            'Country',
            'State',
            'City',
            'CertFile',
            'Diamond Image',
            'Diamond Video'
          ];
          expect(headers).to.deep.equal(expectedHeaders);
          expectedHeaders.forEach((header, index) => {
            cy.log(`Header ${index + 1}: ${header}`);
          });
        });
      });
    });
  }
  verifyRemoveMyStockButton() {
    cy.get(this.removeStockButton).click();
    cy.get(this.removeStockTitle).should('have.text', 'Do you want to remove your stock?');
    cy.get(this.removeInventoryText).should(
      'have.text',
      'This action will remove all the inventory you have on the platform. Please enter Remove in the text field below to continue the process.'
    );
    cy.get(this.cancelButton).should('be.visible');
    cy.get(this.removeMyStockButton).should('be.visible');
    cy.get(this.input).type('Remove');
    cy.get(this.removeMyStockButton).should('be.enabled').click();
    cy.get(this.popup).should('have.text', 'Stock removal in progress, please check after some time.');
  }
  uploadFile(inputFilePath, outputFilePath) {
    if (!inputFilePath.endsWith('.pdf' || '.xlsx')) {
      const inputpath = `${inputFilePath}`;
      const outputPath = `${outputFilePath}`;
      cy.updateStockNumbers(inputpath, outputPath);
    }

    cy.wait(3000);
    cy.get(this.upload, { force: true }).selectFile(outputFilePath, {
      force: true
    });

    if (
      inputFilePath !== '30mb.csv' &&
      !inputFilePath.endsWith('.pdf') &&
      inputFilePath !== 'iceinternatixxxx-stock-file.csv'
    ) {
      cy.get(this.uploadButton).contains('Upload file').click();
      cy.contains('p', 'File was successfully uploaded!', {
        timeout: 60000
      }).should('be.visible');
    }
  }

  uploadFile30MbPopupVerify(text) {
    cy.on('window:alert', (alertText) => {
      expect(alertText).to.equal(`${text}`);
    });
  }
  verifyViewMyStockButton(url) {
    cy.get(this.viewStockButton).click();
    cy.url().should('include', `${url}`);
  }
  verifyUploadFileNameText() {
    const expectedFileName = 'iceinternatixxxx-stock-file.csv';
    cy.get(this.addYourFileText)
      .invoke('text')
      .then((actualText) => {
        expect(actualText.trim()).to.contains(expectedFileName);
      });
  }
  verifyListView() {
    this.getCurrentViewType().then((view) => {
      cy.log(view);
      if (view === 'grid') {
        cy.get(this.listViewBtn, { timeout: 60000 }).should('be.visible').click();
      }
      cy.get(this.listResult, { timeout: 60000 }).should('be.visible');
    });
  }
  verifyGridView() {
    this.getCurrentViewType().then((view) => {
      if (view === 'list') {
        cy.get(this.gridViewBtn, { timeout: 60000 }).should('be.visible').click();
      }
      cy.get(this.gridResult, { timeout: 60000 }).should('be.visible');
    });
  }

  uploadHistory() {
    cy.contains(this.uploadHistoryCta, 'Upload history').should('be.visible').click();
  }

  uploadImg() {
    cy.contains(this.uploadImgCta, 'Upload images').should('be.visible').click();
  }

  fancyColor() {
    cy.get(this.fancyClicked, { timeout: 1500 }).click({ force: true });
  }

  searchSave() {
    cy.wait(2000);
    cy.contains('button', 'Saved searches', { timeout: 1500 }).click({
      force: true
    });
    cy.get(this.saveButtonText).click();
    cy.get(this.searchNameInput).should('be.visible').clear().type('Test search');
    cy.get(this.saveClickAfterText).contains('Save').click();
  }

  defaultSort() {
    cy.contains('button', 'Price: low to high').click();
    cy.wait(500);
    cy.contains(this.defaultClicked, 'Save current sorting as default').click();
  }

  defaultlowCaratSort() {
    cy.contains('button', 'Price: low to high').click();
    cy.get('body')
      .find(this.sortDropDown)
      .should('exist')
      .within(() => {
        cy.contains('Carat: low to high').click();
      });
  }

  defaultPriceSort() {
    cy.contains('button', 'Price: low to high').click();
    cy.get('body')
      .find(this.sortDropDown)
      .should('exist')
      .within(() => {
        cy.contains('Price: high to low').click({ force: true });
      });
  }

  defaulhighCaratSort() {
    cy.contains('button', 'Price: low to high').click();
    cy.get('body')
      .find(this.sortDropDown)
      .should('exist')
      .within(() => {
        cy.contains('Carat: high to low').click();
      });
  }

  defaulDiscountSort() {
    cy.contains('button', 'Price: low to high').click();
    cy.get('body')
      .find(this.sortDropDown)
      .should('exist')
      .within(() => {
        cy.contains('Discount: high to low').click();
      });
  }

  defaulrecentaddedSort() {
    cy.contains('button', 'Price: low to high').click();
    cy.get('body')
      .find(this.sortDropDown)
      .should('exist')
      .within(() => {
        cy.contains('Recently Added').click();
      });
  }

  sampleDiamondFileDownload() {
    cy.contains('button', 'Download sample file').click();
    cy.get('body').contains(this.sampleArrow, 'For Diamond').should('be.visible').click();
  }

  sampleGemsFileDownload() {
    cy.contains('button', 'Download sample file').click();
    cy.get('body').contains(this.sampleArrow, 'For Gemstone').should('be.visible').click();
  }

  getCurrentViewType() {
    return cy.get('body').then(($body) => {
      const isListBtnVisible = $body.find(this.listViewBtn).is(':visible');
      const isGridBtnVisible = $body.find(this.gridViewBtn).is(':visible');

      if (isListBtnVisible && !isGridBtnVisible) {
        return 'grid';
      } else if (isGridBtnVisible && !isListBtnVisible) {
        return 'list';
      } else {
        return cy
          .get(`${this.gridResult}, ${this.listResult}`, { timeout: 200000 })
          .should(($els) => {
            expect($els.length).to.be.greaterThan(0);
          })
          .then(() => {
            const gridCount = Cypress.$(this.gridResult).length;
            const listCount = Cypress.$(this.listResult).length;

            if (gridCount > 0) return 'grid';
            if (listCount > 0) return 'list';

            throw new Error('Results still not detected.');
          });
      }
    });
  }

  disabledUploadButtonAssertion() {
    cy.get(this.uploadButton).contains('Upload file').should('be.disabled');
  }

  getPreviousTime() {
    cy.get(this.supplierUploadStockNav).eq(1).should('be.visible').click();
    cy.get(this.invalidStone, { timeout: 90000 }).should('be.visible');
    cy.get('body')
      .then(($body) => {
        const timeElements = $body.find(this.time);
        if (timeElements.length === 0 || !timeElements.eq(0).text().trim()) {
          this.previousUploadDate = dayjs('1970-01-01T00:00:00Z');
        } else {
          const text = timeElements.eq(0).text().trim();
          this.previousUploadDate = this.parseUploadDate(text);
        }
      })
      .then(() => {
        cy.get(this.supplierUploadStockNav).eq(0).should('be.visible').click();
      });
  }

  parseUploadDate(dateStr) {
    return dayjs(dateStr, 'MMM DD YYYY, hh:mm:ss A');
  }

  gemstoneDropdown() {
    cy.get(this.uploadStockDropDown).click();
    cy.contains('Gemstone').click();
  }

  getLatestTimeAndStatus(retryCount = 3, interval = 15000, attempt = 1) {
    if (attempt === 1) {
      return this.openUploadHistory().then(() => this.checkIfUploadAppeared(retryCount, interval, attempt));
    } else {
      return this.waitForTableToAppear().then(() => this.checkIfUploadAppeared(retryCount, interval, attempt));
    }
  }

  waitForTableToAppear() {
    return cy.get(this.table, { timeout: 20000 }).should('be.visible');
  }

  retryOrReload(message, retryCount, interval, attempt) {
    if (attempt >= retryCount) {
      throw new Error(message);
    }
    cy.log(`Retry ${attempt}: ${message}`);
    return cy
      .wait(interval)
      .then(() => cy.reload())
      .then(() => this.waitForTableToAppear())
      .then(() => this.getLatestTimeAndStatus(retryCount, interval, attempt + 1));
  }

  validateUploadStatus(retryCount, interval, attempt) {
    return cy
      .get(this.status, { timeout: 90000 })
      .eq(0)
      .invoke('text')
      .then((statusText) => {
        const status = statusText.trim().toLowerCase();
        if (status === 'processing' || status === 'pending') {
          return this.retryOrReload('Upload still processing...', retryCount, interval, attempt);
        }
        expect(['partial upload', 'completed']).to.include(status);
        return cy.wrap(null);
      });
  }

  validateUploadTime(retryCount, interval, attempt) {
    return cy
      .get(this.time, { timeout: 90000 })
      .eq(0)
      .invoke('text')
      .then((newText) => {
        if (!newText || !newText.trim()) {
          return this.retryOrReload('No upload time found', retryCount, interval, attempt);
        }
        const newDate = this.parseUploadDate(newText);
        if (!newDate.isAfter(this.previousUploadDate)) {
          return this.retryOrReload('Upload time not newer', retryCount, interval, attempt);
        }
        cy.log(`New upload time: ${newDate.format()}`);
        return this.validateUploadStatus(retryCount, interval, attempt);
      });
  }

  checkIfUploadAppeared(retryCount, interval, attempt) {
    return cy
      .get(this.time, { timeout: 90000 })
      .its('length')
      .then((length) => {
        if (length === 0) {
          return this.retryOrReload('No upload entries found', retryCount, interval, attempt);
        }
        return this.validateUploadTime(retryCount, interval, attempt);
      });
  }

  openUploadHistory() {
    return cy
      .get(this.viewDetailsButton, { timeout: 150000 })
      .should('be.visible')
      .click()
      .then(() => cy.get(this.supplierUploadStockNav).eq(1).should('be.visible').click())
      .then(() => this.waitForTableToAppear());
  }

  uploadXlsxFile(fileName) {
    // Use the passed fileName dynamically
    cy.fixture(fileName, 'binary')
      .then(Cypress.Blob.binaryStringToBlob)
      .then((fileContent) => {
        const testFile = new File([fileContent], fileName, {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(testFile);
        cy.get(this.upload).then(($input) => {
          $input[0].files = dataTransfer.files;
          cy.wrap($input).trigger('change', { force: true });
        });
      });

    cy.get(this.uploadButton).contains('Upload file').click();
  }

  certSearch(fixtureFileName) {
    cy.fixture(fixtureFileName).then((data) => {
      const certNumber = data[0]?.certNumber;

      if (!certNumber) {
        throw new Error('Certificate number not found in fixture: ${fixtureFileName}');
      }

      cy.wait(3000);

      cy.get(this.certInput, { timeout: 50000 }).should('exist').and('be.visible').clear().type(certNumber);

      cy.get('body', { timeout: 30000 }).then(($body) => {
        if ($body.text().includes(certNumber)) {
          cy.contains(certNumber).should('be.visible');
        } else if ($body.find(this.noResult).length > 0) {
          throw new Error('No result found: ${certNumber}');
        }
      });
    });
  }

  myStockOnly() {
    cy.get(this.myStockOnlyButton, { timeout: 2000 }).click();
  }

  applyNaturalDiamondFilters(cert, shape, carat, color, clarity) {
    cy.get(this.certFilter, { timeout: 1000 }).contains(cert).click({ force: true });
    cy.get(this.shapeFilter, { timeout: 1000 }).contains(shape).click({ force: true });
    cy.get(this.caratFilter, { timeout: 2000 }).contains(carat).eq(0).click({ force: true }).scrollIntoView();
    cy.get(this.mainMenuFilter, { timeout: 1000 }).contains(color).click({ force: true });
    cy.get(this.mainMenuFilter, { timeout: 1000 }).contains(clarity).click({ force: true });
  }

  applyLabFilters(cut, fluorescence) {
    cy.get(this.cutFilter, { timeout: 1000 }).contains(cut).scrollIntoView().click({ force: true });
    cy.get(this.mainMenuFilter, { timeout: 1000 }).contains(fluorescence).click({ force: true });
  }

  eyeCleanFilter() {
    cy.get(this.eyeCleanBgm, { timeout: 1000 }).contains('Yes').click({ force: true });
  }

  verifyAdvancedClicked() {
    cy.get(this.advancedCTA).scrollIntoView().click({ force: true });
  }

  shadeFilter() {
    cy.get(this.eyeCleanBgm, { timeout: 1000 }).contains('No BGM').click({ force: true });
  }

  gemsFilters(certificate, type, treatment) {
    cy.get(this.eyeCleanBgm).contains(certificate).click({ force: true });
    cy.get(this.eyeCleanBgm).contains(type).scrollIntoView().click({ force: true });
    cy.get(this.eyeCleanBgm).contains(treatment).scrollIntoView().click({ force: true });
  }

  updateProfileImg() {
    cy.get(this.profileUploadInput).selectFile(this.imgPath, { force: true });
    cy.get(this.successMessage, { timeout: 10000 })
      .should('be.visible')
      .and('have.text', 'Successfully uploaded photo');
  }

  updateName() {
    cy.get(this.firstNameInput).clear().type('Test');
    cy.get(this.saveButton).contains('Save').click();
    cy.get(this.successMessage).should('contain.text', 'Profile updated successfully');
  }

  verifyStatus() {
    cy.get(this.toggleCheckbox).then(($checkbox) => {
      if (!$checkbox.is(':checked')) {
        cy.wrap($checkbox).check({ force: true });
        cy.get(this.saveButton).contains('Save').click();
      }
    });
  }

  updatePassword() {
    cy.get(this.currentPasswordInput).type('Nivoda123');
    cy.get(this.newPasswordInput).type('Nivoda123');
    cy.get(this.confirmPasswordInput).type('Nivoda123');
    cy.get(this.saveButton).contains('Save').click();
    cy.get(this.successMessage).should('contain.text', 'Password updated successfully');
  }

  updateWebsite() {
    cy.get(this.websiteInput).clear().type('www.Nivoda.com');
    cy.get(this.saveButton).contains('Save').click();
    cy.get(this.successMessage).should('contain.text', 'Profile updated successfully');
  }

  addNewUser(successMessage) {
    const firstName = uniqueNamesGenerator({ dictionaries: [names] });
    const lastName = uniqueNamesGenerator({ dictionaries: [names] });
    cy.get(this.addUserButton).contains('Add user').click();
    cy.get(this.firstNameInput).clear().type(firstName);
    cy.get(this.lastNameInput).clear().type(lastName);
    cy.get(this.emailInput).clear().type(`${firstName}${lastName}@Nivoda.net`);
    cy.get(this.phoneNumberInput).clear().type('+923327400111');
    cy.get(this.locationName).clear().type('USA');
    cy.get(this.locationNameOption).click();
    cy.get(this.saveButton).contains('Save').click();
    cy.get(this.confirmLocationAddedMessage, { timeout: 4000 }).should('have.text', successMessage);
    cy.url().should('include', 'settings/user-management');
  }

  addNewLocation() {
    cy.get(this.addUserButton).contains('Add location').click();
    cy.get(this.locationNameInput).clear().type('New street visit');
    cy.get(this.countryInputSelector).filter(':visible').first().clear().type('IND');
    cy.contains('li', /^India$/i, { timeout: 10000 })
      .should('be.visible')
      .click();
    cy.get(this.stateInput).parent().find(this.stateDropClick).click();
    cy.get(this.stateOption, { timeout: 10000 }).click({ force: true });
    cy.get(this.postalCodeInput).clear().type('400094');
    cy.get(this.cityInput).clear().type('Mumbai');
    cy.get(this.addressLineInput).clear().type('Neelyog');
    cy.contains('button', 'Save changes', { timeout: 15000 }).click();
    cy.get(this.locationSuccessMessage, { timeout: 10000 })
      .should('be.visible')
      .and('contain.text', 'Location updated successfully');
  }

  priceRankClick() {
    cy.get(this.priceRank, { timeout: 1000 }).contains('Download price rank CSV').click();
    cy.contains(
      "We're generating your price rank file. An email will be sent to you once it's ready. Please allow up to an hour before trying again."
    ).should('be.visible');
  }

  deleteUser() {
    cy.get(this.trashIcon).eq(2).should('exist').click({ force: true });
    cy.get(this.deleteCTA).contains('Delete').should('be.visible').click();
    cy.get(this.successToast).should('contain.text', 'User deleted successfully');
  }

  ownerCannotBeDeleted() {
    cy.get(this.trashIcon).first().click({ force: true });
    cy.get(this.ownerDeleteMsg).should('contain.text', "Owner profile can't be deleted");
    cy.get(this.cancelBtn).first().should('be.visible').click();
  }

  deleteLocation() {
    cy.get(this.trashIcon).first().should('be.visible').click({ force: true });
    cy.get(this.deleteCTA).contains('Delete').should('be.visible').click();
    cy.get(this.successToast).should('contain.text', 'Location deleted successfully');
  }

  searchLocation(searchText) {
    cy.get(this.searchInput).click().clear().type(searchText);
    cy.get('body').then(($body) => {
      if ($body.find(`${this.searchResult}:contains("${searchText}")`).length) {
        cy.get(this.searchResult).contains(searchText).should('be.visible');
      } else if ($body.find(this.noResultMessage).length) {
        cy.get(this.noResultMessage).should('be.visible');
      }
    });
  }

  setDefaultLocation() {
    cy.get(this.setDefaultStarIcon).should('be.visible').eq(0).click({ force: true });
    cy.get(this.setLocationSuccessMessage).should('be.visible').and('contain.text', 'Operation successful');
  }

  updateLocation() {
    cy.get(this.editIcon).eq(0).click();
    cy.get(this.postalCodeInput).clear().type('410020');
    cy.contains('button', 'Save changes', { timeout: 15000 }).click();
    cy.get(this.locationSuccessMessage, { timeout: 10000 })
      .should('be.visible')
      .and('contain.text', 'Location updated successfully');
  }

  filteredStonesCheck() {
    cy.get(this.bottomTableCount)
      .eq(7)
      .invoke('text')
      .then((text) => {
        const count = Number(text.trim());
        if (count === 1) {
          cy.log('Success');
        } else {
          throw new Error(`Expected count to be 1`);
        }
      });
  }

  updateGemCertificateDelistStatus() {
    cy.get(this.supplierUploadStockNav).eq(0).should('be.visible').click();
    cy.updateDecert();
  }

  checkAndClickInTheTopandBottomTable(topIndex, bottomIndex, indexTop, indexBottom) {
    cy.get(this.topTableCount)
      .eq(topIndex)
      .invoke('text')
      .then((text) => {
        const topTableCount = Number(text.trim());
        if (topTableCount === 1) {
          const dynamicSelector = `:nth-child(${topIndex + 1}) > a`;
          cy.get(dynamicSelector).eq(indexTop).click();
          cy.get(this.reason, { timeout: 150000 }).eq(1).should('exist');
          cy.get(this.back).click();
        } else {
          throw new Error(`Expected count to be 1`);
        }
      });
    cy.get(this.bottomTableCount, { timeout: 150000 })
      .eq(bottomIndex)
      .invoke('text')
      .then((text) => {
        const bottomTableCount = parseInt(text.trim());
        const dynamicSelectorBottumTable = `:nth-child(${bottomIndex + 1}) > a`;
        if (bottomTableCount === 1) {
          cy.get(dynamicSelectorBottumTable).eq(indexBottom).click();
          cy.get(this.reason, { timeout: 150000 }).eq(1).should('exist');
        }
      });
  }

  deleteUser() {
    cy.get(this.trashIcon).eq(2).should('exist').click({ force: true });
    cy.get(this.deleteCTA).contains('Delete').should('be.visible').click();
    cy.get(this.successToast).should('contain.text', 'User deleted successfully');
  }

  ownerCannotBeDeleted() {
    cy.get(this.trashIcon).first().click({ force: true });
    cy.get(this.ownerDeleteMsg).should('contain.text', "Owner profile can't be deleted");
    cy.get(this.cancelBtn).first().should('be.visible').click();
  }

  deleteLocation() {
    cy.get(this.trashIcon).first().should('be.visible').click({ force: true });
    cy.get(this.deleteCTA).contains('Delete').should('be.visible').click();
    cy.get(this.successToast).should('contain.text', 'Location deleted successfully');
  }

  searchLocation(searchText) {
    cy.get(this.searchInput).click().clear().type(searchText);
    cy.get('body').then(($body) => {
      if ($body.find(`${this.searchResult}:contains("${searchText}")`).length) {
        cy.get(this.searchResult).contains(searchText).should('be.visible');
      } else if ($body.find(this.noResultMessage).length) {
        cy.get(this.noResultMessage).should('be.visible');
      }
    });
  }

  setDefaultLocation() {
    cy.get(this.setDefaultStarIcon).should('be.visible').eq(0).click({ force: true });
    cy.get(this.setLocationSuccessMessage).should('be.visible').and('contain.text', 'Operation successful');
  }

  updateLocation() {
    cy.get(this.editIcon).eq(0).click();
    cy.get(this.postalCodeInput).clear().type('410020');
    cy.contains('button', 'Save changes', { timeout: 15000 }).click();
    cy.get(this.locationSuccessMessage, { timeout: 10000 })
      .should('be.visible')
      .and('contain.text', 'Location updated successfully');
  }

  filteredStonesCheck() {
    cy.get(this.bottomTableCount)
      .eq(7)
      .invoke('text')
      .then((text) => {
        const count = Number(text.trim());
        if (count === 1) {
          cy.log('Success');
        } else {
          throw new Error(`Expected count to be 1`);
        }
      });
  }

  updateGemCertificateDelistStatus() {
    cy.get(this.supplierUploadStockNav).eq(0).should('be.visible').click();
    cy.updateDecert();
  }
  uploadImgTab() {
    cy.contains(this.uploadImgCta, 'Upload images').should('be.visible').click();
  }

  deleteImage() {
    cy.get(this.checkboxSelector).click();
    cy.get(this.deleteIconSelector).eq(2).click();
    cy.get(this.deleteCTA).contains('Delete').click();
    cy.get(this.deletionSnackbar).should('be.visible').and('contain.text', 'Diamond image deleted successfully');
  }

  verifyImguploadPopup() {
    cy.get(this.uploadImgClicked, { timeout: 2000 }).contains('Upload image').click();
    cy.get(this.uploadImgClicked, { timeout: 1500 }).contains('Browse files').click();
  }

  selectValueDropdownOption() {
    cy.get(this.dropdownToggleButton).contains('Filter by status').click();
    cy.contains('label', 'Not linked').find(this.dropdownOption).check({ force: true });
    cy.get(this.applyButton, { timeout: 2000 }).contains('Apply').click();
    cy.get(this.clearButton, { timeout: 1000 }).contains('Clear').click();
    cy.get(this.dropdownToggleButton).contains('Filter by status').click();
    cy.contains('label', 'Linked').find(this.dropdownOption).check({ force: true });
    cy.get(this.applyButton, { timeout: 2000 }).contains('Apply').click();
  }
}
export default Supplier;
