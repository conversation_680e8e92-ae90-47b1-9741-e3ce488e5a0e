/// <reference types="cypress" />
class Menu {
  constructor() {
    // Locators
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.hamBurgerMenu = '[data-automation-id="web--ham-btn"]';
    this.sideBarButton = '[data-automation-id="sidebar-menu-btn"]';
    this.supplierHamBurgerMenu = '.MuiButton-root.css-hc0lcc';
    this.supplierSideBarButton = '.MuiListItem-root.MuiListItem-gutters.css-1iewp06';
    this.supplierHamBurgerMenu = '.MuiButton-root.css-hc0lcc';
    this.supplierSideBarButton = '.MuiListItem-root.MuiListItem-gutters.css-1iewp06';
    this.orderpageTitle = '[data-automation-id="header-title"]';
    this.orderDropDown = '[data-automation-id="select--inp"]';
    this.logoutButton = '[data-automation-id="acc-logout"]';
    this.pageRoot = '#root';
    this.BuyerDashboard = '.link-button.link-hover-bg[target="_blank"]';
    this.supplierDashboard = '.link-button.link-hover-bg';
    this.pswdClick = 'a[href="/settings/password"]';
    this.companyClick = 'a[href="/settings/company"]';
    this.userClick = 'a[href="/settings/user-management"]';
    this.locationClick = 'a[href="/settings/locations"]';
  }

  visitMenu() {
    cy.get(this.hamBurgerMenu, { timeout: 90000 }).click({ force: true });
  }

  visitShortList() {
    const shortlistRegex = /Shortlist|Favoris|Elenco breve|Lista de favoritos|お気に入り/;

    cy.get(this.sideBarButton)
      .contains(shortlistRegex, { timeout: 60000 })
      .scrollIntoView()
      .should('be.visible')
      .click()
      .wait(300);

    cy.url().should('include', '/shortlist');
  }

  visitOrdersPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).then(($el) => {
      cy.containsTranslation('orders', { withinSubject: $el }).click();
    });
    cy.url().should('include', 'diamonds/customer-orders/all');
    cy.get(this.orderpageTitle, { timeout: 60000 }).invoke('text').should('eq', 'Orders');
  }

  VerifyLogout() {
    cy.get(this.logoutButton).then(($el) => {
      cy.containsTranslation('log_out', { withinSubject: $el }).click();
    });
  }

  visitSettingsPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).then(($el) => {
      cy.containsTranslation('settings', { withinSubject: $el }).click();
    });
    cy.wait(4000);
    cy.containsTranslation('personal_settings');
    cy.url().should('include', '/settings/personal');
  }

  visitInvoicesPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).then(($el) => {
      cy.containsTranslation('invoices', { withinSubject: $el }).click();
    });
    cy.containsTranslation('invoices');
    cy.url().should('include', '/invoices');
  }

  visitFinancesPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).then(($el) => {
      cy.containsTranslation('finances', { withinSubject: $el }).click();
    });
    cy.url().should('include', '/finances');
  }

  visitRequestPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).then(($el) => {
      cy.containsTranslation('quotes_and_requests', { withinSubject: $el }).click();
    });
    cy.containsTranslation('quotes_and_requests');
    cy.url().should('include', '/live/requests');
  }

  visitHoldsPage() {
    this.visitMenu();
    cy.get(this.sideBarButton)
      .should('be.visible')
      .then(($el) => {
        cy.containsTranslation('holds', { withinSubject: $el }).click();
      });
    cy.containsTranslation('holds');
    cy.url().should('include', '/customer-holds/all');
  }

  visitFeedCenterPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).then(($el) => {
      cy.containsTranslation('feed_center', { withinSubject: $el }).click();
    });
    cy.containsTranslation('feed_center');
    cy.url().should('include', '/feed-center');
  }

  visitFeedSetupPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).then(($el) => {
      cy.containsTranslation('feed_setup', { withinSubject: $el }).click();
    });
    cy.url().should('include', '/live/self-serve');
  }

  accessSupplierTabs(sidebarTab, url) {
    cy.get(this.hamBurgerMenu, { timeout: 90000 }).eq(0).should('be.visible').click({ force: true });
    cy.wait(500);

    cy.intercept('POST', Cypress.env('supplierApiUrl'), (req) => {
      if (req.body.operationName === 'getDiamondOfferList' || req.body.operationName === 'getGemstoneOfferList') {
        req.alias = 'getproduct';
      }
    });

    const tryClickSidebar = () => {
      cy.get('body').then(($body) => {
        if ($body.find(this.supplierSideBarButton).length > 0) {
          cy.get(this.supplierSideBarButton, { timeout: 90000 })
            .contains(sidebarTab)
            .scrollIntoView()
            .should('be.visible')
            .click();
        } else {
          cy.reload();
          cy.wait(1000);
          cy.get(this.supplierSideBarButton, { timeout: 90000 })
            .contains(sidebarTab)
            .scrollIntoView()
            .should('be.visible')
            .click();
        }
      });
    };

    tryClickSidebar();

    cy.url({ timeout: 10000 }).should('include', url);
    cy.get(this.pageRoot).should('not.contain.text', 'Something went wrong');
  }

  visitBuyerDashboardPage() {
    cy.get(this.BuyerDashboard).then(($el) => {
      cy.containsTranslation('buyer_dashboard', { withinSubject: $el }).should('be.visible').click();
    });
  }

  visitSupplierDashboardPage() {
    cy.get(this.supplierDashboard).then(($el) => {
      cy.containsTranslation('supplier_dashboard', { withinSubject: $el }).should('be.visible').click();
    });
  }

  allFilterMenu() {
    cy.contains('button', 'All filters', { timeout: 2000 }).then(($el) => {
      cy.containsTranslation('all_filters', { withinSubject: $el }).click();
    });
  }

  applyFilters() {
    cy.contains('button', 'Apply filters', { timeout: 2000 }).then(($el) => {
      cy.containsTranslation('apply_filters', { withinSubject: $el }).click();
    });
  }

  visitSupplierSettingsPage() {
    this.visitMenu();
    cy.get(this.sideBarButton).then(($el) => {
      cy.containsTranslation('settings', { withinSubject: $el }).click();
    });
    cy.url().should('include', '/settings/personal');
  }

  visitSupplierUserPage() {
    this.visitSupplierSettingsPage();
    cy.get(this.userClick).click();
    cy.url().should('include', '/settings/user-management');
  }

  visitSupplierLocationPage() {
    this.visitSupplierSettingsPage();
    cy.get(this.locationClick).click();
    cy.url().should('include', '/settings/locations');
  }

  visitSupplierCompanySettingsPage() {
    this.visitSupplierSettingsPage();
    cy.get(this.companyClick).click();
    cy.url().should('include', '/settings/company');
  }

  visitSupplierPasswordSettingsPage() {
    this.visitSupplierSettingsPage();
    cy.get(this.pswdClick).click();
    cy.url().should('include', '/settings/password');
  }
}
export default Menu;
