/// <reference types="cypress" />

class Requests {
  constructor() {
    // Selectors
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.diamondTab = '.tab-wrapper-default ';
    this.meleeTab = '.tab-wrapper-default ';
    this.filterButton = '[data-automation-id="filter-by"]';
    this.searchInput = '[data-automation-id="search-input"]';
    this.tableColumn = '#table_header__row > .table__row >';
    this.copyButton = '[data-automation-id="copy__to__clipboard"]';
    this.viewButton = '.row-action';
    this.cancelRequestBtn = '.melee-req-detail-page--request-info-actions > :nth-child(2)';
    this.cancelContainer = '.req-cancel-modal-container-1';
    this.cancelBtn = '.dangerVariant';
    this.popUpContainer = '.new-toaster-message';
    this.meleeAlertBanner = '.melee-alert-banner-desc';
    this.requestNote = '.req-no-edit-note';
    this.commentsBtn = '.tab-wrapper-large ';
    this.comments = '.req-comments';
    this.backBtn = '.melee-req-detail-page--back';
    this.requestStatus = '.req-status';
    this.tableRow = '[data-automation-id="table-row"]';
    this.rejectQuoteButton = '.req-quote-card-quote-actions';
    this.rejectQuoteModal = '.cancel-req-modal';
    this.meleeAlertBannerTitle = '.melee-alert-banner-title';
    this.srPageTabs = '.tab-wrapper-large';
    this.srCommentInputBox = '.req-comments-form-input';
    this.addCommentButton = '.req-comments-form-actions-add';
    this.srCommentCard = '.req-comments-card';
    this.srCommentEmptySection = '.req-comments-empty-section';
    this.loader = '[loading="true"]';
  }
  diamondAndMeleeSectionAssertion() {
    const defaultTimeout = 5000;

    cy.get(this.diamondTab, { timeout: defaultTimeout })
      .should('be.visible')
      .then(($el) => {
        cy.containsTranslation('diamond', { withinSubject: $el }).click();
      });
    cy.get(this.meleeTab, { timeout: defaultTimeout })
      .should('be.visible')
      .then(($el) => {
        cy.containsTranslation('melee', { withinSubject: $el }).click();
      });
  }

  meleeSectionAssertion() {
    const defaultTimeout = 5000;

    cy.get(this.meleeTab, { timeout: 5000 }).then(($el) => {
      cy.containsTranslation('melee', { withinSubject: $el }).click();
    });
    cy.get(this.tableColumn, { timeout: 5000 }).then(($el) => {
      const expectedColumns = ['request_number', 'melee_type', 'melee_info', 'comments', 'status', 'date_requested'];
      expectedColumns.forEach((columnKey) => {
        cy.containsTranslation(columnKey, { withinSubject: $el });
      });
    });
  }

  meleeSrDetailPageAssertion() {
    const defaultTimeout = 5000;

    cy.get(this.viewButton, { timeout: defaultTimeout }).eq(0).should('be.visible').click();
    cy.get(this.loader, { timeout: defaultTimeout }).should('not.exist');

    cy.url().should('include', 'v2/live/requests/melee/SR');
  }

  searchSR(fixtureFileName) {
    const defaultTimeout = 8000;

    cy.readFixtureFile(fixtureFileName).then((data) => {
      const requestNumber = data[0].order_request_number;

      cy.wait(6000);
      cy.get(this.searchInput, { timeout: defaultTimeout })
        .should('be.visible')
        .should('be.enabled')
        .clear()
        .type(`${requestNumber}{enter}`);
      cy.get(this.tableRow, { timeout: defaultTimeout }).should('contain.text', requestNumber);
    });
  }

  cancelSrRequest() {
    const defaultTimeout = 10000;

    cy.get(this.cancelRequestBtn, { timeout: defaultTimeout }).should('be.visible').click();
    cy.get(this.cancelContainer, { timeout: defaultTimeout }).should('contain.text', 'Confirm cancel request');
    cy.get(this.cancelBtn, { timeout: defaultTimeout }).click();
    cy.get(this.popUpContainer, { timeout: defaultTimeout }).should('contain.text', 'Request cancelled successfully');
    cy.get(this.meleeAlertBanner, { timeout: defaultTimeout }).should(
      'contain.text',
      'This request was cancelled by you'
    );
    cy.get(this.requestNote, { timeout: defaultTimeout }).should('contain.text', 'Request no longer editable');
    cy.get(this.commentsBtn, { timeout: defaultTimeout })
      .eq(1)
      .then(($el) => {
        cy.containsTranslation('comments', { withinSubject: $el }).click();
      });
    cy.get(this.comments, { timeout: defaultTimeout }).should(
      'contain.text',
      'Comments are no longer available because this request is now closed.'
    );
    cy.get(this.backBtn, { timeout: defaultTimeout }).click();
    cy.get(this.requestStatus, { timeout: defaultTimeout }).should('contain.text', 'Cancelled');
  }

  rejectSrQuotation() {
    const defaultTimeout = 10000;
    cy.get(this.rejectQuoteButton).scrollIntoView().should('be.visible').contains('button', 'Reject').click();
    cy.get(this.rejectQuoteModal, { timeout: defaultTimeout }).should('be.visible');
    cy.get(this.cancelBtn).should('be.visible').contains('Reject quote').click();
    cy.get(this.popUpContainer, { timeout: defaultTimeout })
      .should('be.visible')
      .contains('Quote updated successfully');
    cy.get(this.meleeAlertBanner, { timeout: defaultTimeout })
      .scrollIntoView()
      .should('contain.text', 'You rejected this quote.');
    cy.get(this.requestStatus, { timeout: defaultTimeout }).scrollIntoView().contains('Rejected');
  }

  acceptSrQuotation() {
    const defaultTimeout = 10000;
    cy.get(this.rejectQuoteButton).scrollIntoView().should('be.visible').contains('button', 'Accept').click();
    cy.get(this.rejectQuoteModal, { timeout: defaultTimeout }).should('be.visible');
    cy.get(this.rejectQuoteModal).contains('button', 'Accept quote').click();
    cy.get(this.popUpContainer, { timeout: defaultTimeout })
      .should('be.visible')
      .contains('Quote updated successfully');
    cy.get(this.meleeAlertBannerTitle, { timeout: defaultTimeout })
      .scrollIntoView()
      .should('contain.text', 'Quote accepted');
    cy.get(this.requestStatus, { timeout: defaultTimeout }).scrollIntoView().contains('Confirmed');
  }

  srComment() {
    cy.get(this.srPageTabs, { timeout: 8000 }).then(($el) => {
      cy.containsTranslation('comments', { withinSubject: $el }).click();
    });
    cy.get(this.srCommentInputBox, { timeout: 5000 }).should('be.visible').clear().type('Test Comment by Nivoda QA');
    cy.get(this.addCommentButton, { timeout: 3000 })
      .should('be.visible')
      .then(($el) => {
        cy.containsTranslation('add_comment', { withinSubject: $el }).click();
      });
    cy.get(this.srCommentCard, { timeout: 8000 })
      .should('be.visible')
      .should('contain.text', 'Test Comment by Nivoda QA');
  }

  noSrCommentModal() {
    cy.get(this.srPageTabs, { timeout: 8000 }).contains('Comments').click();
    cy.get(this.srCommentEmptySection, { timeout: 5000 })
      .should('be.visible')
      .contains('Comments are no longer available because this request is now closed.');
  }
}

export default Requests;
