/// <reference types="cypress" />
class Login {
  constructor() {
    // Locators

    this.email = '#login-email';
    this.password = '#login-password';
    this.submitButton = '[data-automation-id="login-submit"]';
    this.forgotPassword = '[data-automation-id="login-forgot-password"]';
    this.resetPasswordButton = '[data-automation-id= "form-submit-button"]';
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.hamBurgerMenu = '[data-automation-id="web--ham-btn"]';
    this.forgetPasswordEmail = '#forget-password-email';
  }

  visitPage() {
    cy.visit('/');
    cy.get(this.spinner, { timeout: 60000 }).should('not.exist');
  }

  loginUsingUi(emailFilePath) {
    cy.fixture(emailFilePath).then((emailData) => {
      const email = emailData[0].email;
      cy.get(this.email).type(email);
      cy.get(this.password).type('Nivoda123');

      cy.get(this.submitButton).click();
      if (emailFilePath !== 'InvalidUser.json') {
        cy.get(this.submitButton, { timeout: 180000 }).should('not.exist');
      }
    });
    if (emailFilePath === 'loginasadmin.json') {
      cy.wait(2000);
      cy.visit('/admin/orders/purchase-order');
      cy.get(this.spinner, { timeout: 60000 }).should('not.visible');
    }
  }

  loginUsingUniqueUsersApi() {
    cy.task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `SELECT u."email" AS "email"
            FROM "Users" u
                    INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                    INNER JOIN "Companies" c ON c.id = u."CompanyId"
                    INNER JOIN "CompanyFinanceSettings" cf ON cf."CompanyId" = u."CompanyId"
                    INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
                    LEFT JOIN (SELECT * FROM "CartItems") ci ON u.id = ci."UserId"
            WHERE u."role" = 'CUSTOMER'
              AND u."default_currency" = 'USD'
              AND cs."cert_details_enabled" = true
              AND u."verifyStatus" = '4'
              AND u."status" = '4'
              AND u."address_verified" = 'true'
              AND u."LocationId" IS NOT NULL
              AND u.geo_country is not null
              AND u."OwnerOfCompanyId" IS NOT NULL
              AND cs."display_supplier_name" = true
              AND ci."UserId" IS NULL
              AND c.code_disabled is null
              AND cf."DISABLE_CHECKOUT" = false
              AND ck.kyc_verified = true
            LIMIT 1;`
    }).then((result) => {
      if (result.rows.length > 0) {
        const userEmail = result.rows[0].email;
        Cypress.env('loginEmail', userEmail);

        cy.log('User Email:', userEmail);

        return cy
          .request({
            method: 'POST',
            chromeWebSecurity: false,
            url: Cypress.env('loginApiUrl'),
            failOnStatusCode: false,
            body: {
              variables: {
                username: userEmail,
                password: 'Nivoda123',
                twofactorauth: null
              },
              query: `
              query AuthenticateUser($username: String!, $password: String!, $twofactorauth: String) {
                authenticate {
                  username_and_password(
                    username: $username
                    password: $password
                    twofactorauth: $twofactorauth
                  ) {
                    __typename
                    token
                    expires
                    refresh_token
                    hubspot_token
                    user {
                      __typename
                      id
                      firstName
                      lastName
                      email
                      role
                      preferred_language
                      country
                      steps_required
                      company {
                        __typename
                        id
                        name
                        loupe360_url
                        company_settings {
                          __typename
                          CompanyId
                          holds_enabled
                          accept_holds
                          supersaver_enabled
                          company_code
                        }
                        market_pay_active
                        disable_checkout
                        all_in_pricing
                      }
                    }
                  }
                }
              }
            `
            }
          })
          .then((response) => {
            if (response.body.errors && response.body.errors.length > 0) {
              cy.log('Login failed with error:', response.body.errors[0].message);
              assert.fail('Login failed with error: ' + response.body.errors[0].message);
            } else {
              const sessionData = response.body.data.authenticate.username_and_password;
              cy.log(sessionData);
              localStorage.setItem('graphql_session', JSON.stringify(sessionData));
              cy.emptyCartItems();
              cy.emptyShortListCartItems();
              cy.emptyMemoWalletItems();
              cy.fixture('graphqltranslation.json').then((apiData) => {
                const translationApiUrl = Cypress.env('translationApiUrl');

                cy.intercept('POST', translationApiUrl, (req) => {
                  if (req.url.includes('/graphql-translation') && req.method === 'POST') {
                    req.reply({
                      statusCode: 200,
                      body: apiData.response
                    });
                  }
                }).as('stubbedRequest');
              });
              cy.visit('/v2/live/search/natural/diamond/', { timeout: 60000 });
              cy.wait('@stubbedRequest');
              cy.get(this.hamBurgerMenu, { timeout: 120000 }).should('be.visible');
            }
          });
      }
    });
  }

  loginUsingApi(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((fixtureData) => {
      const email = fixtureData[0].email;
      console.log(email);
      Cypress.env('loginEmail', email);

      return cy
        .request({
          method: 'POST',
          chromeWebSecurity: false,
          url: Cypress.env('loginApiUrl'),
          failOnStatusCode: false,
          body: {
            variables: {
              username: email,
              password: 'Nivoda123',
              twofactorauth: null
            },
            query: `
              query AuthenticateUser($username: String!, $password: String!, $twofactorauth: String) {
                authenticate {
                  username_and_password(
                    username: $username
                    password: $password
                    twofactorauth: $twofactorauth
                  ) {
                    __typename
                    token
                    expires
                    refresh_token
                    hubspot_token
                    user {
                      __typename
                      id
                      firstName
                      lastName
                      email
                      role
                      preferred_language
                      country
                      steps_required
                      company {
                        __typename
                        id
                        name
                        loupe360_url
                        company_settings {
                          __typename
                          CompanyId
                          holds_enabled
                          accept_holds
                          supersaver_enabled
                          company_code
                        }
                        market_pay_active
                        disable_checkout
                        all_in_pricing
                      }
                    }
                  }
                }
              }
            `
          }
        })
        .then((response) => {
          if (response.body.errors && response.body.errors.length > 0) {
            cy.log('Login failed with error:', response.body.errors[0].message);
            assert.fail('Login failed with error: ' + response.body.errors[0].message);
          } else {
            const sessionData = response.body.data.authenticate.username_and_password;
            cy.log(sessionData);
            localStorage.setItem('graphql_session', JSON.stringify(sessionData));
            cy.emptyCartItems();
            cy.emptyShortListCartItems();
            cy.emptyMemoWalletItems();
            cy.fixture('graphqltranslation.json').then((apiData) => {
              const translationApiUrl = Cypress.env('translationApiUrl');

              cy.intercept('POST', translationApiUrl, (req) => {
                if (req.url.includes('/graphql-translation') && req.method === 'POST') {
                  req.reply({
                    statusCode: 200,
                    body: apiData.response
                  });
                }
              }).as('stubbedRequest');
            });
            cy.visit('/v2/live/search/natural/diamond/', { timeout: 60000 });
            cy.wait('@stubbedRequest');
            cy.get(this.hamBurgerMenu, { timeout: 120000 }).should('be.visible');
          }
        });
    });
  }

  loginUsingAdminApi(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((fixtureData) => {
      const email = fixtureData[0].email;
      Cypress.env('loginEmail', email);

      return cy
        .request({
          method: 'POST',
          chromeWebSecurity: false,
          url: Cypress.env('loginApiUrl'),
          failOnStatusCode: false,
          body: {
            variables: {
              username: email,
              password: 'Nivoda123',
              twofactorauth: null
            },
            query: `
              query AuthenticateUser($username: String!, $password: String!, $twofactorauth: String) {
                authenticate {
                  username_and_password(
                    username: $username
                    password: $password
                    twofactorauth: $twofactorauth
                  ) {
                    __typename
                    token
                    expires
                    refresh_token
                    hubspot_token
                    user {
                      __typename
                      id
                      firstName
                      lastName
                      email
                      role
                      preferred_language
                      country
                      steps_required
                      company {
                        __typename
                        id
                        name
                        loupe360_url
                        company_settings {
                          __typename
                          CompanyId
                          holds_enabled
                          accept_holds
                          supersaver_enabled
                          company_code
                        }
                        market_pay_active
                        disable_checkout
                        all_in_pricing
                      }
                    }
                  }
                }
              }
            `
          }
        })
        .then((response) => {
          if (response.body.errors && response.body.errors.length > 0) {
            cy.log('Login failed with error:', response.body.errors[0].message);
            assert.fail('Login failed with error: ' + response.body.errors[0].message);
          } else {
            const sessionData = response.body.data.authenticate.username_and_password;
            cy.log(sessionData);
            localStorage.setItem('graphql_session', JSON.stringify(sessionData));
            cy.visit('/admin/orders/purchase-order', { timeout: 60000 }).then(() => { });
            cy.get(this.spinner, { timeout: 60000 }).should('not.visible');
          }
        });
    });
  }
  loginAsCustomerAssertion() {
    cy.get(this.spinner, { timeout: 60000 }).should('not.visible');
    cy.get(this.hamBurgerMenu, { timeout: 120000 }).should('be.visible');
    cy.url().should('include', '/diamond');
  }
  loginAsSupplierAssertion() {
    cy.url({ timeout: 60000 }).should('include', 'upload/stock');
  }
  loginAsDashboardEnabledSupplierAssertion() {
    cy.url({ timeout: 60000 }).should('include', '/dashboard');
  }
  loginAsAdminAssertion() {
    cy.get(this.spinner, { timeout: 60000 }).should('not.visible');
    cy.url({ timeout: 60000 }).should('include', '/admin');
  }
  loginAsInvalidUserAssertion() {
    cy.contains('Your email and/or password is incorrect. Please check your credentials and try again.');
  }
  loginAsInvalidPasswordAssertion() {
    cy.get(this.spinner, { timeout: 60000 }).should('not.visible');
    cy.url().should('include', '/admin');
  }
  resetPass(paramEmailField) {
    cy.get(this.forgotPassword).click();
    cy.get(this.spinner, { timeout: 60000 }).should('not.exist');
    cy.get(this.forgetPasswordEmail).type(paramEmailField);
    cy.get(this.resetPasswordButton).click();
  }
}
export default Login;
