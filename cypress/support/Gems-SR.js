/// <reference types="cypress" />
class GemsSR {
  constructor() {
    this.checkoutBanner = '[data-automation-id="checkout-banner"]';
    this.gemsMakeRequestBanner = '.gem_not_found';
    this.specialRequestModal = '.gems_special_request_modal.popup';
    this.modalCloseButton = '[data-automation-id="close-btn"]';
    this.step2NextButton = '[data-testid="special_request--step2"]';
    this.colorSwitchBox = '[data-testid="special_request_color--switchbox"]';
    this.naturalLabel = '.meleeColorSwitchBox__label:contains("Natural")';
    this.labGrownLabel = '.meleeColorSwitchBox__label:contains("Lab grown")';
    this.gemsSRConfirmToast = '.new-toaster-title';
    (this.gemstoneOption = '.checkbox--value span'),
      (this.fromInput = '#from-input-field'),
      (this.toInput = '#to-input-field');
  }

  verifyColorsVisible() {
    cy.get(this.colorSwitchBox).within(() => {
      cy.contains('Natural').should('be.visible');
      cy.contains('Lab grown').should('be.visible');
    });
  }

  clickNatural() {
    cy.get(this.colorSwitchBox).contains('Natural').click();
  }

  validateOptionsVisible(options, selector) {
    options.forEach((option) => {
      cy.contains(selector, option).should('be.visible');
    });
  }

  gemsSRFormStep1Assertions() {
    this.assertStaticTexts();
    this.selectCertifiedOption();
    this.verifyColorsVisible();
    this.clickNatural();
    this.selectGemstoneOptions();
    this.selectShapeAndColor();
    this.enterCaratRange();
    this.selectTreatmentOptions();
    this.selectOriginAndProceed();
    cy.contains('Review & add comments').should('be.visible');
  }

  assertStaticTexts() {
    cy.contains('Make a request');
    cy.contains('We will respond within 24 hours');
    cy.contains('Gemstone details');
    cy.contains('Please provide as much information as possible on the type of gemstone you’re looking for');
  }

  selectCertifiedOption() {
    cy.contains('Certified').should('be.visible').click();
  }

  selectGemstoneOptions() {
    const gemstoneOptions = [
      'Sapphire',
      'Emerald',
      'Ruby',
      'Tourmaline',
      'Tanzanite',
      'Aquamarine',
      'Alexandrite',
      'Spinel',
      'Opal',
      'Topaz',
      'Garnet',
      'Morganite',
      'Peridot',
      'Zircon',
      'Amethyst',
      'Kunzite'
    ];

    this.validateOptionsVisible(gemstoneOptions, this.gemstoneOption);
    cy.contains(this.gemstoneOption, 'Sapphire').click();
  }

  selectShapeAndColor() {
    cy.contains('Round').click();
    cy.contains('Green').click();
  }

  enterCaratRange() {
    cy.get(this.fromInput).clear().type('1');
    cy.get(this.toInput).clear().type('5').blur();
  }

  selectTreatmentOptions() {
    const treatmentOptions = [
      'None',
      'Heating',
      'Heating & pressure',
      'Oiling',
      'Enhancement',
      'Composite',
      'Dyeing',
      'Waxing',
      'Coating',
      'Diffusion',
      'Filling',
      'Imitation',
      'Bleaching',
      'Irradiation',
      'Impregnated',
      'Lasering'
    ];

    this.validateOptionsVisible(treatmentOptions, '.checkbox--value span');
    cy.contains('.checkbox--value span', 'Heating').click();
  }

  selectOriginAndProceed() {
    cy.contains('India').click({ force: true });
    cy.get(this.step2NextButton).contains('Next').click({ force: true });
  }

  gemsSRFormStep2Assertions() {
    const expectedTexts = [
      'Yes',
      'Natural',
      'Sapphire',
      'Round',
      'Green',
      '1 - 5 ct',
      'Heating',
      'India',
      'Back',
      'Step 2 of 2',
      'Submit request'
    ];

    cy.get('.popup')
      .should('be.visible')
      .within(() => {
        expectedTexts.forEach((text) => {
          cy.contains(text, { matchCase: false, timeout: 5000 }).scrollIntoView().should('be.visible');
        });
      });

    cy.get(this.step2NextButton, { timeout: 5000 }).should('be.visible').contains('Submit request');
  }

  gemsSpecialRequestSubmission() {
    cy.get(this.step2NextButton, { timeout: 5000 }).should('be.visible').contains('Submit request').click();
    cy.get(this.gemsSRConfirmToast)
      .scrollIntoView()
      .should('be.visible', { timeout: 5000 })
      .invoke('text')
      .then((text) => {
        const normalizedText = text.trim().replace(/\s+/g, ' ');
        expect(normalizedText).to.include('Request SR-');
        expect(normalizedText).to.include('created successfully!');
        cy.contains('We will send you a confirmation email shortly.').should('be.visible');
      });
  }

  verifySpecialRequestBannerAndModal() {
    cy.get(this.checkoutBanner, { timeout: 60000 })
      .eq(0)
      .find('span')
      .first()
      .then(($nav) => {
        cy.containsTranslation('make_a_request', { withinSubject: $nav }).then(() => {
          cy.wrap($nav).click({ force: true });
        });
      });
    cy.get(this.specialRequestModal).eq(0).should('be.visible');
  }

  verifySpecialRequestBannerFilterModal() {
    cy.get(this.checkoutBanner, { timeout: 60000 })
      .eq(1)
      .find('span')
      .last()
      .then(($nav) => {
        cy.containsTranslation('make_a_request', { withinSubject: $nav }).then(() => {
          cy.wrap($nav).click({ force: true });
        });
      });
    cy.get(this.specialRequestModal).eq(1).should('be.visible');
  }

  closeSRModal() {
    cy.get(this.modalCloseButton).last().click();
  }
}

export default GemsSR;
