Cypress.Commands.add('getDestinationForApiUser', (email) => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: ` SELECT l.id as id
    FROM "Locations" l
    INNER JOIN "Users" u ON l."CompanyId" = u."CompanyId"
    WHERE u.email = '${email}'
    AND l.default_shipping_address = true
    LIMIT 1;
  `
  }).then((result) => {
    if (result && result.rows.length > 0) {
      const destinationId = result.rows[0].id;
      cy.log('📌 Destination found:', destinationId);

      Cypress.env('destinationId', destinationId);

      cy.writeFile('cypress/fixtures/getDestinationForApiUser.json', result.rows);
    } else {
      cy.log('⚠️ No destination found for this email.');
    }
  });
});

Cypress.Commands.add('authenticateUser', (fixtureFile, password, twoFactorAuth = null) => {
  cy.fixture(fixtureFile).then((userData) => {
    const { email: userEmail, id: userId } = userData[0];

    cy.request({
      method: 'POST',
      url: Cypress.env('apiurl'),
      headers: { 'Content-Type': 'application/json' },
      body: {
        query: `
          query AuthenticateUser($username: String!, $password: String!, $twofactorauth: String) {
            authenticate {
              username_and_password(username: $username, password: $password, twofactorauth: $twofactorauth) {
                token
              }
            }
          }
        `,
        variables: { username: userEmail, password, twofactorauth: twoFactorAuth || undefined }
      }
    }).then((response) => {
      expect(response.status).to.eq(200);
      Cypress.env('authToken', response.body.data.authenticate.username_and_password.token);
      cy.log('Auth Token', response.body.data.authenticate.username_and_password.token);

      return cy.wrap({ userId, userEmail });
    });
  });
});

Cypress.Commands.add('searchStone', (certNumber) => {
  cy.api({
    method: 'POST',
    url: Cypress.env('apiurl'),
    headers: {
      Authorization: `Bearer ${Cypress.env('authToken')}`,
      'Content-Type': 'application/json'
    },
    body: {
      query: `
        query platformSearch($query: DiamondQuery, $offset: Int, $order: DiamondOrder) {
          diamonds: offers_by_query(limit: 24, offset: $offset, query: $query, order: $order) {
            total_count
            items { id }
            certificates {
              availability
              certificate { certNumber }
            }
          }
        }
      `,
      variables: {
        query: { certificate_numbers: [certNumber], custom_query: false, is_user_supplier: false },
        offset: 0,
        order: { type: 'price', direction: 'ASC' }
      }
    }
  }).then((response) => {
    expect(response.status).to.eq(200);
    const diamondId = response.body.data.diamonds.items[0].id;
    Cypress.env('offerId', diamondId);
    cy.log('💎 Diamond Offer ID:', diamondId);
  });
});

Cypress.Commands.add(
  'searchDiamondsByFilter',
  (filters = {}, offset = 0, orderBy = { type: 'price', direction: 'ASC' }) => {
    return cy
      .api({
        method: 'POST',
        url: Cypress.env('apiurl'),
        headers: {
          Authorization: `Bearer ${Cypress.env('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: {
          variables: {
            query: { custom_query: true, ...filters },
            offset: offset,
            order: orderBy
          },
          query: `
        query platformSearch($query: DiamondQuery, $offset: Int, $order: DiamondOrder) {
          diamonds: offers_by_query(limit: 24, offset: $offset, query: $query, order: $order) {
            total_count
            items {
              id
              price
              Product {
                ... on Diamond {
                  supplierStockId
                }
              }
            }
            certificates {
              availability
              certificate {
                certNumber
                shape
                carats
                color
                clarity
              }
            }
          }
        }
      `
        }
      })
      .then((response) => {
        expect(response.status).to.eq(200);
        const diamonds = response.body.data.diamonds;

        cy.log(`💎 Found ${diamonds.total_count} diamonds`);
      });
  }
);

Cypress.Commands.add('updateCart', () => {
  cy.api({
    method: 'POST',
    url: Cypress.env('apiurl'),
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Cypress.env('authToken')}`
    },
    body: {
      query: `
        mutation updateCart($items: [InputCartItem!]!) {
          update_cart(items: $items) {
            id
            cart_items {
              offer { id }
              destination { id }
            }
          }
        }
      `,
      variables: {
        items: [
          {
            carats: null,
            pieces: null,
            offer_id: Cypress.env('offerId'),
            destination_id: Cypress.env('destinationId'),
            delivery_type: 'STANDARD',
            specific_date: null,
            added_at: Date.now(),
            customer_comment: '',
            customer_order_number: '',
            return_option: false
          }
        ]
      }
    }
  }).then((response) => {
    expect(response.status).to.eq(200);
    const cartItems = response.body.data.update_cart.cart_items;

    if (cartItems.length > 0) {
      cy.log('Destination ID', cartItems[0].destination?.id);
      Cypress.env('offerId', cartItems[0].offer?.id);
      cy.log('Offer ID', cartItems[0].offer?.id);
    }
  });
});

Cypress.Commands.add('requestAuth', () => {
  cy.api({
    method: 'POST',
    url: Cypress.env('apiurl'), // Update with your actual GraphQL endpoint
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Cypress.env('authToken')}`
    },
    body: {
      query: `mutation { request_auth }`,
      variables: {}
    }
  }).then((response) => {
    expect(response.status).to.eq(200);
    cy.log('Auth Request Successful:', response.body.data.request_auth);
  });
});
Cypress.Commands.add('getAPIOTP', () => {
  return cy
    .task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `
      SELECT code, "createdAt"
      FROM "CodeConfirms"
      ORDER BY "createdAt" DESC
      LIMIT 1`
    })
    .then((result) => {
      const latestOTP = result.rows[0].code;
      cy.log(latestOTP);
      Cypress.env('otp', latestOTP);
    });
});

Cypress.Commands.add('createOrder', (options = {}) => {
  const {
    paymentTerm = 'up_front_payment',
    deliveryDate = Date.now(),
    comment = '',
    customerComment = '',
    customerOrderNumber = '',
    returnOption = false,
    isMemoItem = false
  } = options;

  cy.api({
    method: 'POST',
    url: Cypress.env('apiurl'),
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Cypress.env('authToken')}`
    },
    body: {
      query: `
        mutation CreateOrder($order: OrderInput!, $code: String!, $order_method: String) {
          create_order(order: $order, code: $code, order_method: $order_method) {
            id
          }
        }
      `,
      variables: {
        code: Cypress.env('otp'),
        order: {
          is_free_shipment: false,
          items: [
            {
              destinationId: Cypress.env('destinationId'),
              offerId: Cypress.env('offerId'),
              deliveryDate,
              customer_comment: customerComment,
              customer_order_number: customerOrderNumber,
              return_option: returnOption,
              is_memo_item: isMemoItem
            }
          ],
          comment,
          payment_term_selected: paymentTerm,
          payment_term_fee: 0
        },
        order_method: 'platform'
      }
    }
  }).then((response) => {
    if (response.body.errors && response.body.errors.length > 0) {
      const errorMsg = response.body.errors[0]?.message || 'Unknown error occurred.';
      cy.log(`❌ Order creation failed: ${errorMsg}`);
      // Set a custom flag or throw an error if you want to fail the test
      response.status = 400; // Manually setting this if needed downstream
      throw new Error(`Order creation failed: ${errorMsg}`);
    }

    expect(response.status).to.eq(200);
    const orderId = response.body.data?.create_order?.id;
    Cypress.env('orderid', orderId);
    cy.log(`✅ Order created successfully: ${orderId}`);
  });
});

Cypress.Commands.add('getOrder', (orderId) => {
  return cy
    .api({
      method: 'POST',
      url: Cypress.env('apiurl'),
      body: {
        operationName: 'getOrder',
        variables: {
          id: Cypress.env('orderid')
        },
        query: `query getOrder($id: ID!) {
          order(id: $id) {
            id
            items {
              order_number
            }
          }
        }`
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Cypress.env('authToken')}`
      }
    })
    .then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('data');
      cy.log('✅ Order Number:', JSON.stringify(response.body.data.order.items[0].order_number, null, 2));
    });
});
Cypress.Commands.add('searchGemStone', (certificateNumber) => {
  return cy
    .api({
      method: 'POST',
      url: Cypress.env('apiurl'),
      body: {
        operationName: 'GemstonesOfferByQuery',
        variables: {
          offset: 0,
          query: {
            certificate_numbers: [certificateNumber],
            custom_query: true
          },
          order: {
            type: 'price',
            direction: 'ASC'
          }
        },
        query: `query GemstonesOfferByQuery($query: GemstoneQuery, $offset: Int, $order: GemstoneOrder) {
          gemstones: gemstone_offers_by_query(
            limit: 1
            offset: $offset
            query: $query
            order: $order
          ) {
            items {
              id
            }
            certificates {
              certificate {
                certNumber
              }
            }
          }
        }`
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Cypress.env('authToken')}`
      }
    })
    .then((response) => {
      expect(response.status).to.eq(200);
      cy.log(response.body);
      const gemstoneid = response.body.data.gemstones.items[0].id;
      const certificateNumberres = response.body.data.gemstones.certificates[0]?.certificate?.certNumber;
      cy.log('📜 Certificate Number:', certificateNumberres);
      expect(certificateNumberres).to.eq(certificateNumber);
      Cypress.env('offerId', gemstoneid);
      cy.log('💎 Gemstone Offer ID:', gemstoneid);
    });
});

Cypress.Commands.add('searchmelee', (certificateNumber, type = 'NATURAL') => {
  return cy
    .api({
      method: 'POST',
      url: Cypress.env('apiurl'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Cypress.env('authToken')}`
      },
      body: {
        operationName: 'getMeleeOfferList',
        variables: {
          query: {
            supplierStockId: [certificateNumber],
            type: type
          },
          order: {
            direction: 'ASC',
            type: 'price'
          }
        },
        query: `
        query getMeleeOfferList($query: MeleeSearchQuery, $offset: Int, $order: MeleeOrder) {
          melees: melee_offers_by_query(
            limit: 24
            offset: $offset
            query: $query
            order: $order
          ) {
            items {
              id
              Product {
                ... on Melee {
                  supplierStockId
                  certificate {
                    carats
                  }
                }
              }
            }
          }
        }
      `
      }
    })
    .then((response) => {
      expect(response.status).to.eq(200);

      expect(response.status).to.eq(200);
      cy.log(response.body);
      const meleeid = response.body.data.melees.items[0].id;
      const product = response.body.data.melees.items[0].Product;
      const supplierStockId = product.supplierStockId;
      const carats = product.certificate?.carats;
      const pieces = carats ? Math.round(1 / carats) : 0;
      cy.log('📜 SupplierStockId Number:', supplierStockId);
      expect(supplierStockId).to.eq(certificateNumber);

      cy.log('💎 Melee Offer ID:', meleeid);
      cy.log('🔹 pieces:', pieces);

      return cy.wrap({ meleeid, pieces, carats });
    });
});

Cypress.Commands.add('updateMeleeCart', (meleeid, pieces) => {
  return cy
    .api({
      method: 'POST',
      url: Cypress.env('apiurl'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Cypress.env('authToken')}`
      },
      body: {
        query: `
        mutation updateCart($items: [InputCartItem!]!) {
          update_cart(items: $items) {
            id
            cart_items {
              offer {
                id
                price
                Product {
                  ... on Melee {
                    id
                    carats
                    carats_ordered
                  }
                }
              }
              destination {
                id
              }
              delivery_type
              specific_date
              carats
              pieces
            }
          }
        }
      `,
        variables: {
          items: [
            {
              added_at: Date.now(),
              carats: 1,
              customer_comment: '',
              customer_order_number: '',
              delivery_type: 'STANDARD',
              destination_id: null,
              offer_id: meleeid,
              pieces: pieces,
              return_option: false,
              specific_date: null,
              customer_preference: 'carats'
            }
          ]
        }
      }
    })
    .then((response) => {
      expect(response.status).to.eq(200);
      const cartItems = response.body.data.update_cart.cart_items;
      if (cartItems.length > 0) {
        cy.log('Destination ID', cartItems[0].destination?.id);
        Cypress.env('offerId', cartItems[0].offer?.id);
        cy.log('Offer ID', cartItems[0].offer?.id);
      }
    });
});

Cypress.Commands.add('pdpDataAPI', (id) => {
  return cy
    .api({
      method: 'POST',
      url: Cypress.env('apiurl'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Cypress.env('authToken')}`
      },
      body: {
        variables: { id },
        query: `
        query ($id: ID!) {
          diamond: diamond_by_id(id: $id) {
            id
            price
            supplierStockId
            diamondSupplierStockId
            availability
            supplier {
              id
              name
              company_image
              rating
              preferred
              __typename
            }
            certificate {
              id
              certNumber
              shape
              carats
              color
              clarity
              lab
              __typename
            }
            __typename
          }
        }
        `
      }
    })
    .then((response) => {
      expect(response.status).to.eq(200);
      const diamond = response.body.data.diamond;

      if (diamond) {
        cy.log('💎 Diamond Details:');
        cy.log(`Price: ${diamond.price}`);
        cy.log(`Supplier Stock ID: ${diamond.supplierStockId}`);
        cy.log(`Supplier: ${diamond.supplier?.name}`);
        cy.log(`Certificate: ${diamond.certificate?.certNumber}`);
      } else {
        cy.log('⚠️ No diamond found with ID:', id);
      }

      return cy.wrap(diamond);
    });
});

Cypress.Commands.add(
  'searchGemstoneByFilter',
  (filters = {}, offset = 0, orderBy = { type: 'price', direction: 'ASC' }) => {
    return cy
      .api({
        method: 'POST',
        url: Cypress.env('apiurl'),
        headers: {
          Authorization: `Bearer ${Cypress.env('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: {
          variables: {
            query: { custom_query: true, ...filters },
            offset: offset,
            order: orderBy
          },
          query: `
      query GemstonesOfferByQuery($query: GemstoneQuery, $offset: Int, $order: GemstoneOrder) {
        gemstones: gemstone_offers_by_query(
          limit: 24
          offset: $offset
          query: $query
          order: $order
        ) {
          total_count
          items {
            id
            price
            Product {
              ... on Gemstone {
                id
                supplierStockId
                gemstoneSupplierStockId
                supplier {
                  id
                  name
                }
              }
            }
          }
          certificates {
            availability
            certificate {
              certNumber
              shape
              carats
              clarity
              color
              gemType
            }
          }
        }
      }`
        }
      })
      .then((response) => {
        expect(response.status).to.eq(200);
        const gemstones = response.body.data.gemstones;

        cy.log(`💎 Found ${gemstones.total_count} gemstones`);
      });
  }
);

Cypress.Commands.add(
  'searchMeleeByFilter',
  (filters = {}, offset = 0, orderBy = { type: 'price', direction: 'ASC' }) => {
    return cy
      .api({
        method: 'POST',
        url: Cypress.env('apiurl'),
        headers: {
          Authorization: `Bearer ${Cypress.env('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: {
          operationName: 'getMeleeOfferList',
          variables: {
            query: filters,
            offset: offset,
            order: orderBy
          },
          query: `
      query getMeleeOfferList($query: MeleeSearchQuery, $offset: Int, $order: MeleeOrder) {
        melees: melee_offers_by_query(
          limit: 24
          offset: $offset
          query: $query
          order: $order
        ) {
          total_count
          items {
            id
            price
            price_per_carat
            Product {
              ... on Melee {
                id
                supplierStockId
                carats
                pieces
                type
                supplier {
                  id
                  name
                }
                certificate {
                  shape
                  color
                  clarity
                  cut
                }
              }
            }
          }
        }
      }`
        }
      })
      .then((response) => {
        expect(response.status).to.eq(200);
        const melees = response.body.data.melees;
        const items = melees.items || [];

        cy.log(`💎 Found ${melees.total_count} `);

        if (items.length > 0) {
          const firstMelee = items[0];
          const product = firstMelee.Product;

          cy.log('💰 Price:', firstMelee.price);
          cy.log('💎 Supplier Stock ID:', product.supplierStockId);
          cy.log('💎 Carats:', product.carats);
          cy.log('💎 Pieces:', product.pieces);
        }
      });
  }
);

Cypress.Commands.add('addStoneToMemoWallet', () => {
  return cy
    .api({
      method: 'POST',
      url: Cypress.env('apiurl'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Cypress.env('authToken')}`
      },
      body: {
        operationName: 'updateMemoWallet',
        variables: {
          items: [
            {
              destination_id: Cypress.env('destinationId'),
              offer_id: Cypress.env('offerId'),
              customer_reference: null,
              order_notes: null,
              needed_by_date: null
            }
          ]
        },
        query: `
        mutation updateMemoWallet($items: [InputMemoWalletItem!]!) {
          update_memo_wallet(items: $items) {
            id
            memo_wallet_items {
              offer {
                id
                price
                Product {
                  ... on Diamond {
                    id
                    supplierStockId
                    certificate {
                      certNumber
                      shape
                      carats
                      color
                      clarity
                    }
                  }
                  ... on Gemstone {
                    id
                    supplierStockId
                  }
                }
              }
              destination {
                id
              }
              added_at
              customer_reference
              order_notes
              needed_by_date
            }
          }
        }
      `
      }
    })
    .then((response) => {
      expect(response.status).to.eq(200);
    });
});
