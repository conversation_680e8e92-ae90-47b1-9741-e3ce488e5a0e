/// <reference types="cypress" />
const fs = require('fs');
import { faker } from '@faker-js/faker';

class Checkout {
  constructor() {
    // Locators
    this.unverifiedBanner = '[data-automation-id="checkout-banner"]';
    this.continueToDeliveryButton = '[data-automation-id="continue-to-delivery"]';
    this.continueToPaymentButton = '[data-automation-id="continue-to-payment-selection"]';
    this.paymentMethodWrapper = '[data-automation-id="payment-page-wrapper"]';
    this.accountLimitWrapper = '[data-automation-id="account-limit-wrapper"]';
    this.discountPill = '[data-automation-id="pill-wrapper"]';
    this.placeOrderButton = '[data-automation-id="review-place-order"]';
    this.checkBox = '[data-automation-id="checkbox"]';
    this.placeOrderFinishButton = '[data-automation-id="place-order"], [data-automation-id="place-order-button"]';
    this.otpCodeField = '[data-automation-id="otp-input-box"]';
    this.confirmButton = '[data-automation-id="confirm-button"]';
    this.fulfillmentWrapper = '[data-automation-id="order-summary-container"]';
    this.paymentTermsTitle = '[data-automation-id="payment-terms-title"]';
    this.checkoutBanner = '[data-automation-id="checkout-banner"]';
    this.liveChatWrapper = ':nth-child(1) > iframe';
    this.orderConfirmation = '[data-automation-id="checkout-page"]';
    this.orderNo = '[data-automation-id="order-number"]';
    this.multipleOrderNo = '.sc-bMVAic.fqqoLL';
    this.cartItemsInfo = '[data-automation-id="cart-items-basic-info"]';
    this.changeDeliveryButton = '[data-automation-id="change-delivery-for-this-order"]';
    this.addNewAddressButton = '[data-automation-id="add-new-address"]';
    this.addressName = '[data-automation-id="Location name (e.g. Hatton Garden)"]';
    this.countryField = '.css-isze0s-control > .css-hlgwow > .css-19bb58m';
    this.countryMenu = '.css-1nmdiq5-menu';
    this.addressField = '[data-automation-id="Enter address 1"]';
    this.saveAddressButton = 'button[mode="default"][width="auto"]:contains("Save location")';
    this.addressContainer = '.sc-daURTG.dArFQS';
    this.cartItemsContainer = '.review-place-order__cart_items--item';
    this.deliveryOptions = '.delivery-option__delivery-type-list';
    this.checkBoxIcon = '.choice-box';
    this.companyDeliveryAddress = '.sc-bMVAic.fnILAM';
    this.cartInfoSection = '.cart-info-section';
    this.locationDropdown = '[data-automation-id="location-dropdown"]';
    this.locationOptionMenu = '.location-dropdown__option';
    this.locationOptionMenu1 = '.deqIMO';
    this.locationDropdownMenu = '.location-dropdown__menu';
    this.locationDropdownOptions = '.location-dropdown__menu .location-dropdown__option';

    this.registeredAddress = '.delivery-option__address-default';
    this.orderText = ':nth-child(2) > .cart-info-section > :nth-child(3) > :nth-child(1) > .info--value';
    this.locationOptions = '.location-dropdown__menu-list > :nth-child(2) > [class^="sc-"]';
    this.caratsAndPiecesQuantityField = '.ct-pcs-checkout';
    this.messageWrapper = '.go2072408551.notification-wrapper';
    this.cityField = '[data-automation-id="Enter city"]';
    this.stateField = '[data-automation-id="Enter State"]';
    this.postalCodeField = '[data-automation-id="Enter postal code"]';
    this.locationPopUp = '.popup-content:visible';
    this.popUpContinueButton = '.popup-button-continue';
    this.contactUsAnchorLink = 'a[style="text-decoration: underline; font-weight: 600; cursor: pointer;"]';

    this.addressData = {
      branchName: 'Test Branch',
      country: 'United Kingdom',
      streetAddress: faker.location.streetAddress(),
      city: faker.location.city(),
      state: faker.location.state(),
      postalCode: faker.location.zipCode()
    };
  }

  verifyCheckoutPageUrl() {
    cy.url().should('include', '/checkout/cart');
  }

  checkIfCheckoutIsDisabled() {
    this.verifyCheckoutPageUrl();
    cy.get(this.unverifiedBanner, { timeout: 50000 })
      .should('be.visible')
      .contains('Your account is being verified. Checkout is disabled.');
  }

  proceedToDeliveryOptions() {
    cy.get(this.continueToDeliveryButton, { timeout: 50000 })
      .should('be.visible')
      .contains('Continue to delivery options')
      .click();
  }

  proceedToPaymentOptions() {
    cy.get('body').then(($body) => {
      if ($body.find(this.locationPopUp, { timeout: 5000 }).length > 0) {
        cy.get(this.popUpContinueButton, { timeout: 5000 }).click();
        cy.get(this.continueToPaymentButton, { timeout: 90000 })
          .should('be.visible')
          .should('be.enabled')
          .contains('Continue to payment selection')
          .click();
      } else {
        cy.get(this.continueToPaymentButton, { timeout: 90000 })
          .should('be.visible')
          .should('be.enabled')
          .contains('Continue to payment selection')
          .click();
      }
    });
  }

  proceedThroughCheckout() {
    this.proceedToDeliveryOptions();
    this.proceedToPaymentOptions();
  }

  proceedToPlaceOrder() {
    cy.get(this.placeOrderButton, { timeout: 150000 }).should('be.visible').contains('Review Order').click();
  }

  verifyPaymentMethod(text) {
    cy.get(this.paymentMethodWrapper, { timeout: 50000 }).should('be.visible').contains(text);
  }

  verifyKriyaPaymentMethod(text) {
    cy.get(this.paymentTermsTitle, { timeout: 50000 }).should('be.visible').contains(text).click();
  }

  assertAccountLimitBarNotVisible() {
    cy.get(this.accountLimitWrapper, { timeout: 50000 }).should('not.exist');
  }

  verifyAccountLimitBarDetails(fixtureFileName) {
    return cy.fixture(fixtureFileName).then((limitData) => {
      const accountLimit = limitData[0].Total_Account_Limit;
      return cy.get(this.accountLimitWrapper, { timeout: 50000 }).should('be.visible').contains(accountLimit);
    });
  }
  assertDiscountPill(text) {
    cy.get(this.discountPill, { timeout: 50000 }).contains(text);
  }

  placeOrder() {
    cy.get(this.checkBox, { timeout: 50000 }).eq(1).should('be.visible').click();
    cy.get(this.placeOrderFinishButton, { timeout: 50000 }).eq(1).scrollIntoView().should('be.visible');
    cy.get(this.placeOrderFinishButton, { timeout: 50000 }).eq(1).should('not.be.disabled');
    cy.get(this.placeOrderFinishButton, { timeout: 50000 }).eq(1).click();

    cy.wait(3000);
    cy.getOTP(new Date()).then((otpCode) => {
      const otpDigits = otpCode.split('');

      for (let i = 0; i < otpDigits.length; i++) {
        cy.get(this.otpCodeField, { timeout: 150000 }).eq(i).should('be.visible').type(otpDigits[i]);
      }
    });
    cy.get(this.confirmButton, { timeout: 50000 }).should('be.visible').click();
    cy.get(this.otpCodeField, { timeout: 180000 }).should('not.exist');
  }
  verifyConfirmationPageDetails(text1, text2) {
    cy.get(this.fulfillmentWrapper, { timeout: 50000 })
      .eq(0)
      .scrollIntoView({ offset: { top: -50, left: 0 }, easing: 'linear', duration: 500 })
      .should('be.visible')
      .contains(text1);
    cy.wait(500);
    cy.get(this.paymentTermsTitle, { timeout: 50000 })
      .scrollIntoView({ offset: { top: -50, left: 0 }, easing: 'linear', duration: 500 })
      .contains(text2, { timeout: 60000 });
  }

  verifyContactUsOpensLiveChat() {
    cy.get(this.checkoutBanner, { timeout: 50000 }).should('be.visible');
    cy.wait(4000);
    cy.get(this.contactUsAnchorLink).click();

    cy.get(this.liveChatWrapper, { timeout: 50000 }).should('be.visible');
  }
  orderConfirmationText() {
    cy.url({ timeout: 50000 }).should('include', '/checkout/order-confirmation');
    cy.get(this.orderConfirmation, { timeout: 280000 }).contains(
      'Thank you! Your order request is awaiting confirmation.'
    );
    cy.get(this.orderNo)
      .invoke('text')
      .then((orderNo) => {
        const data = {
          orderNumber: orderNo.trim()
        };
        cy.task('writeToFile', {
          filename: 'cypress/fixtures/orderNumber.json',
          data: data
        });
        cy.readFixtureFile('cypress/fixtures/orderNumber.json').then((fileContent) => {
          expect(fileContent).to.have.property('orderNumber', orderNo.trim());
        });
      });
  }

  multipleOrderConfirmationText() {
    cy.url({ timeout: 50000 }).should('include', '/checkout/order-confirmation');
    cy.get(this.orderConfirmation, { timeout: 28000 }).contains(
      'Thank you! Your order request is awaiting confirmation.'
    );
    cy.get(this.orderText)
      .eq(0)
      .invoke('text')
      .then((orderNo) => {
        const data = {
          orderNumber: orderNo.trim()
        };
        cy.task('writeToFile', {
          filename: 'cypress/fixtures/creditstoneorderNumber1.json',
          data: data
        });
      });

    cy.get(this.orderText)
      .eq(1)
      .invoke('text')
      .then((orderNo) => {
        const data = {
          orderNumber: orderNo.trim()
        };
        cy.task('writeToFile', {
          filename: 'cypress/fixtures/creditstoneorderNumber2.json',
          data: data
        });
      });
  }

  verifyReturnOptionOnCheckoutForMelee() {
    cy.get(this.cartItemsInfo, { timeout: 50000 }).should('be.visible').contains('No return option');
  }

  addDeliveryAddress() {
    cy.get(this.changeDeliveryButton, { timeout: 50000 }).should('be.visible').click();
    cy.get(this.addNewAddressButton, { timeout: 50000 }).should('be.visible').click();
    cy.get(this.countryField, { timeout: 50000 }).eq(0).click().type(this.addressData.country);
    cy.wait(2000);
    cy.get(this.countryMenu, { timeout: 50000 }).should('be.visible').contains(this.addressData.country).click();
    cy.get(this.addressField, { timeout: 50000 }).eq(0).clear().type(this.addressData.streetAddress);
    cy.get(this.cityField, { timeout: 50000 }).clear().type(this.addressData.city);
    cy.get(this.stateField, { timeout: 50000 }).clear().type(this.addressData.state);
    cy.get(this.postalCodeField, { timeout: 50000 }).clear().type(this.addressData.postalCode);
    cy.get(this.addressName, { timeout: 50000 }).should('be.visible').clear().type(this.addressData.branchName);
    cy.get(this.saveAddressButton, { timeout: 50000 }).should('be.visible').should('be.enabled').click();
  }

  verifyAddressInStoneDetails() {
    this.proceedToPlaceOrder();

    cy.get(this.cartItemsContainer, { timeout: 50000 }).should('be.visible');
    cy.contains(this.cartItemsContainer, this.addressData.branchName).should('be.visible');
    cy.contains(this.cartItemsContainer, this.addressData.streetAddress).should('be.visible');
    cy.contains(this.cartItemsContainer, this.addressData.city).should('be.visible');
    cy.contains(this.cartItemsContainer, this.addressData.postalCode).should('be.visible');
  }

  deliverToMultipleAddressNotVisible() {
    cy.get(this.changeDeliveryButton, { timeout: 50000 }).should('be.visible');
    cy.get(this.registeredAddress, { timeout: 50000 }).should('be.visible');
    cy.get(this.changeDeliveryButton, { timeout: 50000 }).click();
    cy.get(this.deliveryOptions).should('not.exist');
  }

  defaultAddressSelectionAssertion() {
    cy.get(this.changeDeliveryButton, { timeout: 50000 }).should('be.visible').click();
    cy.get(this.checkBoxIcon, { timeout: 50000 })
      .filter((index, element) => {
        return Cypress.$(element).find('.choice-box__icon svg').length > 0;
      })
      .invoke('text')
      .then((address) => {
        cy.log(`Address: ${address.trim()}`);
        const match = address.match(/^[^\d]+/);
        const trimmedAddress = match ? match[0].trim() : 'No Match Found';

        this.proceedToPaymentOptions();
        this.proceedToPlaceOrder();
        // cy.get(this.cartInfoSection).should('contain.text', trimmedAddress);
      });
  }

  deliverToSingleAddress() {
    cy.get(this.changeDeliveryButton, { timeout: 50000 }).should('be.visible').click();
    cy.get(this.checkBoxIcon, { timeout: 50000 }).eq(2).click();
    cy.get('.go2072408551.notification-wrapper').should('be.visible');
  }

  deliverToMultipleAddresses(fixtureFileName) {
    return cy.fixture(fixtureFileName).then((locationData) => {
      const address1 = locationData[0].location_1;
      const address2 = locationData[0].location_2;

      cy.log(`Address1: ${address1}`);
      cy.log(`Address2: ${address2}`);
      cy.get(this.changeDeliveryButton, { timeout: 50000 }).should('be.visible').click();
      cy.get(this.deliveryOptions, { timeout: 30000 })
        .should('be.visible')
        .contains('Deliver to multiple addresses')
        .scrollIntoView()
        .click();
      cy.get(this.locationDropdown).eq(0).click().get(this.locationOptionMenu).eq(0).click();
      cy.get(this.messageWrapper).contains('Location changed successfully');
      cy.wait(2000);
      cy.get(this.locationDropdown).eq(1).click().get(this.locationOptionMenu).eq(1).click();
      this.proceedToPaymentOptions();
      this.proceedToPlaceOrder();
      cy.contains(address1).should('exist');
      cy.contains(address2).should('exist');
      this.placeOrder();
    });
  }
  deliverToMultipleAddressesForExpress() {
    cy.get(this.changeDeliveryButton, { timeout: 50000 }).should('be.visible').click();
    cy.get(this.deliveryOptions, { timeout: 30000 })
      .should('be.visible')
      .contains('Deliver to multiple addresses')
      .scrollIntoView()
      .click();
    cy.get(this.locationDropdown)
      .eq(1)
      .invoke('text')
      .then((currentLocationText) => {
        cy.log(`Current location: ${currentLocationText.trim()}`);
        cy.get(this.locationDropdown).eq(1).click();
        cy.get(this.locationDropdownMenu)
          .should('be.visible')
          .then(($menu) => {
            cy.get(this.locationDropdownOptions).each(($option, index) => {
              const optionText = $option.text().trim();

              if (optionText !== currentLocationText.trim()) {
                cy.log(`Selected option: ${optionText}`);
                cy.wrap($option).click();
                return false;
              }
              if (index === $option.length - 1) {
                cy.get(this.locationDropdownOptions).first().click();
              }
            });
          });
      });

    cy.get('body').then(($body) => {
      if ($body.find(this.locationPopUp, { timeout: 5000 }).length > 0) {
        cy.get(this.popUpContinueButton, { timeout: 5000 }).click();
      } else {
        cy.get(this.checkoutBanner, { timeout: 10000 })
          .should('be.visible')
          .contains('Delivery address not covered by Express delivery');
      }
    });
  }

  verifyCaratsAnsPiecesQuantity(carats) {
    cy.get(this.caratsAndPiecesQuantityField, { timeout: 5000 })
      .scrollIntoView({ offset: { top: -50, left: 0 }, easing: 'linear', duration: 500 })
      .should('be.visible')
      .invoke('text')
      .then((text) => {
        const normalizedText = text.replace(/\s+/g, ' ').trim();
        cy.log(normalizedText);
        expect(normalizedText).to.include(carats);
      });
  }
}

export default Checkout;
