// ***********************************************************
// This example support/e2e.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';
require('cypress-downloadfile/lib/downloadFileCommand');
import 'cypress-real-events';
import { failedTestToggle } from 'cypress-plugin-last-failed';
import 'cypress-plugin-api';
import './Api-Models/Search-Order-API-Models';
import './Api-Models/Shortlist-API-Models';
import './Api-Models/Hold-API-Models';
import './Api-Models/Request-API-Models';
import './Api-Models/Orders-Page-API-Models';
import './Api-Models/Jewellery-API-Models';
import './Api-Models/Download-Link-API-Models';
import { skipIfPreviousTestsFailed } from 'cypress-skip-this-test';

const registerCypressGrep = require('@bahmutov/cy-grep');
registerCypressGrep();

failedTestToggle();
beforeEach(() => {
  cy.intercept('https://px.ads.linkedin.com/wa/', {
    statusCode: 403,
    body: 'Blocked by Cypress'
  }).as('blockLinkedInAd');
});
beforeEach(() => {
  cy.intercept('POST 200 https://m.stripe.com/6', {
    statusCode: 403,
    body: 'Blocked by Cypress'
  }).as('blockstripe');
});
// Alternatively you can use CommonJS syntax:
// require('./commands')
Cypress.on(
  'uncaught:exception',
  (_err, _runnable) =>
    // returning false here prevents Cypress from
    // failing the test
    false
);
