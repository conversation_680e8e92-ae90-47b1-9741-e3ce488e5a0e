/// <reference types="cypress" />
class Filter {
  constructor() {
    // Locators
    this.mainFilter = '[data-automation-id="filters"]';
    this.minmaxPrice = '[data-automation-id="input-wrapper"]';
    this.applyFilterButton = '[data-automation-id="apply-filter-btn"]';
    this.priceFitler = '[data-automation-id="select--inp"]';
    this.productPrice =
      ':nth-child(1) > .sc-cROsgo > :nth-child(1) > [style="margin-bottom: 8px;"] > .price-text > .price-amount > span';
    this.productDeliveredPrice = '[data-automation-id="price-amount"]';
    this.scrollBar = '#drawer-root_common > div > div.drawer.left > div > div.fd--top-section';
    this.filterTab = 'button[data-automation-id="custom-tab"]';
    this.supplierField =
      '#drawer-root_common > div > div.drawer.left > div > div.fd--top-section > div.fd--content.diamond--filters > div > div:nth-child(4) > div:nth-child(2) > div.supplier-filter--box > div > div > div > div.niv-rs__value-container.niv-rs__value-container--is-multi.css-hlgwow';
    this.selectSupplier = '[id^="react-select-5-option-"]';
    this.meleeShapeFilterWrapper = '[data-automation-id="melee-filters-divider"]';
    this.meleeApplyFilterButton = '[data-automation-id="apply-filters-btn"]';
    this.meleeListViewIcon = '[data-testid="search-melee-list-view-btn"]';
    this.deliverySection = '[data-automation-id="delivery-section"]';
    this.meleeColourFilter = '.meleeFilters__vertical-divider.meleeFilters__padding';
    this.meleeRequestButton = '[data-testid="special_request--provideDetails"]';
    this.meleeFilterWrapper = '.meleeFilter__wrapper';
    this.sizeInputs = '.css-19bb58m';
    this.advancedButton = '[data-automation-id="custom-tab"]';
    this.locationFilterBox = '[data-automation-id="location-filter-box"]';
    this.scsFilterBtn = '.default-btn--label';
    this.fancyColorOption = 'div[data-target-value="fancy"]';
    this.fancyColorRadio = '[data-target-value="fancy"]';
    this.filterMenuButton = '[data-testid="search-melee-filter-btn"]';
    this.radioSelected = '.mPGMf > .sc-iyvyFf > span';
    this.listView = 'span.view--btn-label';
    this.gridView = '.view--btn-label';
    this.shortlisCta = '.shortlist--btn';
    this.addToCart = 'button:contains("Add to cart")';
    this.cartContinue = '.continue_button';
    this.viewCart = '.header__view_cart_text';
    this.checkBox = '[data-automation-id="checkbox"]';
    this.dropdownTrigger = '.css-1uxv76j-singleValue';
    this.optionBaseId = '#react-select-2-option-';
    this.tracrId = '.checkbox--value';
    this.label = 'p.MuiStatus-label';
    this.downloadArrow = 'svg.icon-download';
    this.copyCTA = '[data-testid="copy-button"]';
    this.downloadCTA = 'li.MuiButton-outlinedSecondary:has(svg.icon-download)';
    this.imgGemsDownload = '.MuiList-root > :nth-child(5)';
    this.minPriceInput = '#price-min';
    this.maxPriceInput = '#price-max';
    this.helpButton = 'button:has(h3:contains("Help"))';
    this.helpDropdownMenu = 'ul[role="menu"]';
    this.needHelpMenuItem = 'li[role="menuitem"]:has(h3:contains("Need help?"))';
    this.liveChatWrapper = ':nth-child(1) > iframe';
    this.chatInterface = 'div[role="presentation"]:has(div[role="presentation"])';
    this.chatContainer = 'div[role="presentation"] > div[role="presentation"]';
    this.companyLogo = 'img[alt="Company logo"]';
    this.chatLog = 'div[role="log"]';
    this.chatInputField = 'textarea[placeholder="Type a message"]';
    this.chatSection = 'section.sc-vrqbdz-0';
    this.naturalStatusContainer = 'div.MuiStatus-root.MuiStatus-colorSuccess';
    this.naturalStatusLabel = 'p.MuiTypography-root.MuiTypography-body2.MuiStatus-label';
    this.labStatusContainer = 'div.MuiStatus-root.MuiStatus-colorWarning';
    this.labStatusLabel = 'p.MuiTypography-root.MuiTypography-body2.MuiStatus-label';
    this.statusRoot = 'div.MuiStatus-root';
    this.imageFilterButton = 'button[value="image"]';
    this.videoFilterButton = 'button[value="video"]';
    this.toastMessage = '[data-automation-id="toast"]';
  }

  addMinMaxPrice() {
    cy.get(this.mainFilter).click();
    cy.get(this.scrollBar).scrollTo('bottom'); // Scroll 'sidebar' to its bottom
    cy.get(this.minmaxPrice).eq(0).type('60', { force: true });
    cy.get(this.minmaxPrice).eq(1).type('5000000', { force: true });
    cy.get(this.applyFilterButton).eq(1).click();
  }

  getPricing(dbResult) {
    cy.get(this.productPrice, { timeout: 60000 })
      .should('be.visible')
      .first()
      .invoke('text')
      .then((text) => {
        const productprice = parseFloat(
          text
            .trim()
            .match(/[-+]?[0-9,]*\.?[0-9]+/)[0]
            .replace(/,/g, '')
        );
        console.log(productprice);
        expect(parseFloat(productprice)).to.equal(parseFloat(dbResult));

        cy.get(this.productDeliveredPrice, { timeout: 60000 })
          .should('be.visible')
          .first()
          .invoke('text')
          .then((text) => {
            const productPrice = parseFloat(text.trim().match(/[-+]?[0-9]*\.?[0-9]+/)[0]);
            const productdeliveredprice = (parseFloat(dbResult) + 50).toFixed(2);
            console.log('Procut Delivered Price:', productdeliveredprice);
          });
      });
  }
  applySupplierFilter() {
    cy.get(this.mainFilter).click();
    cy.get(this.filterTab).contains('Advanced').click();
    cy.get(this.supplierField, {
      timeout: 60000
    })
      .should('be.visible')
      .type('1caa2b8d-5d1a-4638-b214-b76b0df96736');
    cy.get(this.selectSupplier).click();
    cy.get(this.applyFilterButton).eq(1).click();
  }

  lowtoHighFilter() {
    cy.task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `SELECT "Diamonds"."valueWithDiscount" FROM "Diamonds"
      INNER JOIN "NaturalCertificates" ON "Diamonds"."CertificateId" = "NaturalCertificates"."id"
      WHERE "Diamonds"."OrderItemId" IS NULL 
      AND "Diamonds"."CertificateType" = 'NaturalCertificate' 
      AND "Diamonds"."CompanyId" = '1caa2b8d-5d1a-4638-b214-b76b0df96736'
      ORDER BY "Diamonds"."valueWithDiscount" ASC Limit 1`
    }).then((result) => {
      const dbResult = result.rows[0].valueWithDiscount;
      console.log('DB Result:', dbResult);
      this.getPricing(dbResult);
    });
  }
  highToLowFilter() {
    cy.task('READFROMDB', {
      dbConfig: Cypress.config('DB'),
      sql: `SELECT "Diamonds"."valueWithDiscount" FROM "Diamonds"
      INNER JOIN "NaturalCertificates" ON "Diamonds"."CertificateId" = "NaturalCertificates"."id"
      WHERE "Diamonds"."OrderItemId" IS NULL 
      AND "Diamonds"."CertificateType" = 'NaturalCertificate' 
      AND "Diamonds"."CompanyId" = '1caa2b8d-5d1a-4638-b214-b76b0df96736'
      ORDER BY "Diamonds"."valueWithDiscount" DESC Limit 1`
    }).then((result) => {
      const dbResult = result.rows[0].valueWithDiscount;
      console.log('DB Result:', dbResult);
      cy.get(this.priceFitler).eq(1).click();
      cy.contains('Price high to low').click();
      this.getPricing(dbResult);
    });
  }

  labgrownMeleeShapeFilterAssertionInBothViews() {
    this.verifyFilterInView('grid');
    cy.get(this.meleeListViewIcon).should('be.visible').click();
    this.verifyFilterInView('list');
  }

  verifyFilterInView(viewType) {
    this.verifyShapeFilter('Round');
    this.verifyShapeFilter('Marquise');
    this.verifyShapeFilter('Pear');

    cy.log(`Verified shape filters in ${viewType} view`);
  }

  verifyShapeFilter(shape) {
    cy.get(this.mainFilter, { timeout: 30000 }).should('be.visible').click({ force: true });
    cy.get(this.meleeShapeFilterWrapper, { timeout: 30000 })
      .should('be.visible')
      .contains(shape)
      .click({ force: true });
    cy.get(this.meleeApplyFilterButton, { timeout: 30000 }).should('be.visible').click({ force: true });
    cy.contains(shape);
  }

  applyFilters() {
    cy.get(this.meleeApplyFilterButton, { timeout: 30000 }).should('be.visible').click({ force: true });
  }

  priceCt() {
    cy.contains('button', 'Price').click();
    cy.get(this.minPriceInput).clear().type('6');
    cy.get(this.maxPriceInput).clear().type('20');
    cy.contains('button', 'Apply filters').click();
  }

  selectAllFilters() {
    cy.get(this.filterMenuButton, { timeout: 10000 }).should('be.visible').click({ force: true });
  }

  selectFancyColor() {
    cy.get(this.fancyColorRadio).eq(0).scrollIntoView().click();
    cy.get(this.fancyColorOption, { timeout: 30000 }).eq(0).should('exist').click();
  }

  selectTracr() {
    cy.get(this.advancedButton, { timeout: 5000 }).contains('Advanced').click();
    cy.get(this.tracrId).contains('Tracr').should('be.visible').click();
  }

  applyDiamondFilters() {
    cy.get(this.applyFilterButton, { timeout: 3000 }).eq(1).should('exist').click();
  }

  gridViewClick() {
    cy.contains(this.listView, 'List View', { timeout: 10000 }).click();
    cy.get(this.gridView).should('contain', 'Grid View');
  }

  HasImg() {
    cy.get(this.imageFilterButton)
      .should('be.visible')
      .should('not.be.disabled')
      .and('contain.text', 'Has image')
      .click({ timeout: 5000 });
  }
  HasVideo() {
    cy.get(this.videoFilterButton)
      .should('be.visible')
      .should('not.be.disabled')
      .and('contain.text', 'Has video')
      .click({ timeout: 3000 });
  }

  verifyArrow() {
    cy.wait('@getproduct');

    cy.get(this.downloadArrow, { timeout: 5000 }).parent('button').eq(4).click();
  }

  copyImgLink() {
    cy.get(this.copyCTA, { timeout: 5000 }).contains('Copy image link').click();
  }
  copyVideoLink() {
    cy.get(this.copyCTA, { timeout: 5000 }).contains('Copy video link').click();
  }

  downloadImg() {
    cy.get(this.downloadCTA, { timeout: 5000 }).contains('Download image').click();
    cy.contains('Image downloaded').should('be.visible');
  }

  downloadVideo() {
    cy.get(this.downloadCTA, { timeout: 5000 }).contains('Download video').click();
    cy.get(this.toastMessage, { timeout: 5000 }).should('be.visible').contains('Video downloaded');
  }

  verifyLabLabel() {
    cy.get(this.labStatusContainer, { timeout: 60000 })
      .eq(0)
      .should('be.visible')
      .should('have.class', 'MuiStatus-colorWarning')
      .within(() => {
        cy.get(this.labStatusLabel, { timeout: 60000 })
          .should('be.visible')
          .should('contain.text', 'Lab')
          .and('have.class', 'MuiStatus-label');
      });
  }
  verifyNaturalLabel() {
    cy.get(this.naturalStatusContainer, { timeout: 60000 })
      .eq(0)
      .should('be.visible')
      .should('have.class', 'MuiStatus-colorSuccess')
      .within(() => {
        cy.get(this.naturalStatusLabel, { timeout: 60000 })
          .should('be.visible')
          .should('contain.text', 'Natural')
          .and('have.class', 'MuiStatus-label');
      });
  }

  helpClicked() {
    cy.get(this.helpButton).should('be.visible').should('not.be.disabled').click({ force: true });

    cy.get(this.helpDropdownMenu)
      .should('be.visible')
      .within(() => {
        cy.get(this.needHelpMenuItem).should('be.visible').click({ force: true });
      });

    cy.verifyLiveChatIframe(this.liveChatWrapper);
  }

  verifyStandardDeliveryTimes(shape, deliveryTimes) {
    this.verifyShapeFilter(shape);
    cy.get(this.deliverySection, { timeout: 60000 }).contains(deliveryTimes);
  }

  verifyColourForMelee() {
    cy.url().then((url) => {
      const expectedColors = url.includes('natural') ? ['D-E-F', 'G-H', 'I-J'] : ['D-E-F', 'G-H'];

      cy.get(this.mainFilter, { timeout: 60000 })
        .should('be.visible')
        .should('not.be.disabled')
        .scrollIntoView()
        .click({ force: true });

      cy.get(this.meleeColourFilter, { timeout: 60000 })
        .should('be.visible')
        .wait(3000)
        .within(() => {
          expectedColors.forEach((color) => {
            cy.contains(color).should('be.visible');
          });
        });
    });
  }

  meleeRequestButtonAssertionsFromFilters() {
    cy.get(this.meleeRequestButton, { timeout: 60000 }).should('be.visible').contains('Make a request').click();
  }

  verifyMeleeRequest() {
    cy.get(this.mainFilter, { timeout: 60000 })
      .should('be.visible')
      .should('not.be.disabled')
      .scrollIntoView()
      .click({ force: true });

    this.meleeRequestButtonAssertionsFromFilters();
  }

  applyMeleeFilters(shape, color, clairty, cut) {
    cy.get(this.mainFilter, { timeout: 30000 }).should('be.visible').click({ force: true });
    cy.get(this.meleeShapeFilterWrapper, { timeout: 30000 })
      .should('be.visible')
      .contains(shape)
      .click({ force: true });
    cy.get(this.meleeColourFilter, { timeout: 60000 })
      .should('be.visible')
      .within(() => {
        cy.contains(color).should('be.visible').click();
      });
    cy.get(this.meleeFilterWrapper, { timeout: 5000 }).should('be.visible').contains(clairty).click();
    cy.get(this.meleeFilterWrapper, { timeout: 5000 }).should('be.visible').contains(cut).click();
  }

  addMeleeStoneSizeFromFilters() {
    cy.get(this.sizeInputs, { timeout: 5000 })
      .eq(3)
      .within(() => {
        for (let i = 0; i < 12; i++) {
          cy.get('input').type('1 {enter}', { force: true });
        }
      });
  }

  applyLocationFilter(country) {
    cy.get(this.mainFilter, { timeout: 60000 })
      .should('be.visible')
      .and('not.be.disabled')
      .scrollIntoView()
      .click({ force: true });
    cy.get(this.advancedButton, { timeout: 5000 }).contains('Advanced').click();
    cy.get(this.locationFilterBox, { timeout: 5000 }).should('be.visible').click();
    cy.get(this.sizeInputs, { timeout: 5000 }).eq(3).find('input').clear().type(`${country}{enter}`, { force: true });
    cy.get(this.applyFilterButton).eq(1).click();
  }

  multiGemshortlist() {
    cy.get(this.checkBox).eq(11).click();
    cy.get(this.checkBox).eq(12).click();
    cy.get(this.checkBox).eq(13).click();
    cy.get(this.shortlisCta).contains('Shortlist').click();
  }

  multiGemsAddToCart() {
    cy.get(this.checkBox).eq(11).click();
    cy.get(this.checkBox).eq(12).click();
    cy.get(this.checkBox).eq(13).click();
    cy.get(this.addToCart).contains('Add to cart').click();
    cy.get(this.cartContinue).contains('Continue and add').click();
    cy.get(this.viewCart).click();
  }

  applyScsFilter() {
    cy.get(this.mainFilter, { timeout: 60000 })
      .should('be.visible')
      .and('not.be.disabled')
      .scrollIntoView()
      .click({ force: true });
    cy.get(this.filterTab).contains('Advanced').click();
    cy.get(this.scsFilterBtn, { timeout: 5000 }).contains('SCS-007').click();
    cy.get(this.applyFilterButton).eq(1).click();
  }

  openMainFilter() {
    cy.get(this.mainFilter, { timeout: 60000 }).should('be.visible').and('not.be.disabled').click({ force: true });
  }
}
export default Filter;
