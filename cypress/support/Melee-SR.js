/// <reference types="cypress" />
class MeleeSR {
  constructor() {
    // Selectors
    this.spinner = '[data-automation-id="loading-overlay"]';
    this.shapeComponent = '[data-testid="special_request_shape--component"]';
    this.shape = '[data-testid="special_request_shape"]';
    this.color = '[data-testid="special_request_color"]';
    this.cut = '[data-testid="special_request_cut"]';
    this.clarity = '[data-testid="special_request_clarity--component"]';
    this.stepMessage = '.step-msz';
    this.colorButton = '[data-testid="special_request_color--component"] > [class^="sc-"] > :nth-child(1)';
    this.nextButton = '[data-testid="special_request--step2"]';
    this.step2NextButton = '[data-testid="special_request--confirmDetails"]';
    this.submitButton = '[data-testid="special_request_confirmDetails"]';
    this.quantityContainer = '.quantity-container';
    this.notesContainer = '[data-testid="special_request_carat--comments"]';
    this.refImageContainer = '[data-testid="special_request_reference_image"]';
    this.inputFields = '[data-automation-id="placeholder"]';
    this.resizableBox = '.resizable__box';
    this.meleeReviewDetails = '[data-testid="meleeSPR_confirmDetails__details"]';
    this.meleeSuccessModal = '.content-area';
    this.copyIcon = '.copy_clipboard__icon.spr_number__icon';
    this.srInfo = '.sr-info';
    this.addSizeBtn = '[data-testid="special_reques--addSize"]';
    this.maxNoteError = '.max-char.note-error';
    this.labels = '.labels';
    this.meleeRequestButton = '[data-testid="special_request--switchButton"]';
    this.meleeRequestButtonFromFilter = '[data-testid="special_request--provideDetails"]';
    this.colorComponent = '[data-testid="special_request_color--component"] > [class^="sc-"]';
    this.sizeInputs = '.inputFields';
    this.closeIcon = '[data-testid="special_request-closeModal"]';
    this.requestConfirmationToast = '.cardTitle';
  }

  meleeSRFormStep1Assertions() {
    cy.contains('Make a request');
    cy.contains('We will respond within 24 hours');
    cy.contains('Melee details');
    cy.contains('Please provide as much information as possible on the type of melee you’re looking for');
    cy.get(this.shapeComponent, { timeout: 5000 }).should('be.visible');
    cy.get(this.shape, { timeout: 5000 }).should('be.visible');
    cy.get(this.color, { timeout: 5000 }).contains('Colour').scrollIntoView().should('be.visible');
    cy.get(this.clarity, { timeout: 5000 }).should('be.visible');
    cy.get(this.cut, { timeout: 5000 }).should('be.visible');
    cy.get(this.stepMessage, { timeout: 5000 }).should('be.visible').contains('Step 1 of 3');
  }

  fillStep1FormForMeleeSR(shape, clairty, cut) {
    cy.get(this.shape, { timeout: 5000 })
      .last()
      .scrollIntoView({ offset: { top: -60, left: 0 }, easing: 'linear', duration: 500 })
      .should('be.visible')
      .contains(shape)
      .click();
    cy.get(this.colorButton).contains('D').click();
    cy.get(this.clarity, { timeout: 5000 }).should('be.visible').contains(clairty).click();
    cy.get(this.cut, { timeout: 5000 }).should('be.visible').contains(cut).click();
    cy.get(this.nextButton).contains('Next').click();
  }

  meleeSRFormStep2Assertions() {
    cy.contains('Quantity & Size');
    cy.contains('Select quantity either by total carat weight or by number of pieces.');
    cy.get(this.quantityContainer, { timeout: 5000 }).should('be.visible');
    cy.get(this.notesContainer, { timeout: 5000 })
      .last()
      .scrollIntoView({ offset: { top: -60, left: 0 }, easing: 'linear', duration: 500 })
      .should('be.visible');
    cy.get(this.refImageContainer, { timeout: 5000 }).contains('Image reference').scrollIntoView().should('be.visible');
    cy.get(this.stepMessage, { timeout: 5000 }).should('be.visible').contains('Step 2 of 3');
  }

  fillStep2FormForMeleeSR() {
    cy.contains('Quantity & Size').scrollIntoView();
    cy.get(this.inputFields).eq(0).dblclick({ force: true }).type('5', { force: true });
    cy.get(this.inputFields).eq(1).dblclick({ force: true }).type('5', { force: true });
    cy.get(this.inputFields).eq(2).dblclick({ force: true }).type('5', { force: true });
    cy.get(this.resizableBox).type('Making a SR request');
    cy.get(this.step2NextButton).contains('Next').click();
  }

  meleeSRFormStep3Assertions(shape, clarity, cut) {
    cy.contains('Review and submit your request');
    cy.contains('Please ensure the below details are correct before submitting your request');
    cy.get(this.meleeReviewDetails, { timeout: 5000 })
      .last()
      .scrollIntoView({ offset: { top: -60, left: 0 }, easing: 'linear', duration: 500 })
      .should('be.visible')
      .contains(shape);
    cy.get(this.meleeReviewDetails, { timeout: 5000 }).should('be.visible').contains(clarity);
    cy.get(this.meleeReviewDetails, { timeout: 5000 }).should('be.visible').contains(cut);
    cy.get(this.stepMessage, { timeout: 5000 }).should('be.visible').contains('Step 3 of 3');
    cy.get(this.requestConfirmationToast).then(($el) => {
      if ($el.text().includes('Customer details')) {
        cy.get(this.submitButton).contains('Submit request').scrollIntoView().click();
        cy.contains('Request Submitted');
      } else {
        cy.get(this.submitButton).contains('Submit request').scrollIntoView().click();
        cy.contains('Request submitted!');
      }
    });
  }

  addParcels() {
    cy.contains('Quantity & Size').scrollIntoView();
    for (var i = 1; i < 10; i++) {
      cy.get(this.addSizeBtn, { timeout: 5000 }).should('be.visible').contains('Add Size').scrollIntoView().click();
    }
    cy.get(this.addSizeBtn, { timeout: 5000 }).should('not.exist');
  }

  maxCharacterAssertion() {
    cy.contains('Quantity & Size').scrollIntoView();
    cy.get(this.inputFields).eq(0).dblclick({ force: true }).type('11', { force: true });
    cy.get(this.inputFields).eq(1).dblclick({ force: true }).type('11', { force: true });
    cy.get(this.inputFields).eq(2).dblclick({ force: true }).type('11', { force: true });
    const testText =
      'This test case is checking the maximum characters allowed in this Note Section. ' +
      'The purpose is to validate that more than 300 characters cannot be added in this section. ' +
      'If the characters are less than 300, the next button should be enabled else it should remain disabled ' +
      'and should not allow the user to move forward.';

    cy.get(this.resizableBox).clear({ force: true }).type(testText, { delay: 0 });
    cy.get(this.resizableBox)
      .invoke('val')
      .then((enteredText) => {
        expect(enteredText).to.not.equal(testText);
      });
  }

  lwdFieldNotVisibleAssertion() {
    cy.get(this.quantityContainer, { timeout: 5000 })
      .should('be.visible')
      .then(() => {
        cy.get(this.labels, { timeout: 5000 })
          .should('be.visible')
          .should('not.contain', 'Length')
          .should('not.contain', 'Width')
          .should('not.contain', 'Depth');
      });
  }

  lwdFieldVisibleAssertion() {
    cy.get(this.quantityContainer, { timeout: 5000 })
      .should('be.visible')
      .then(() => {
        cy.get(this.labels, { timeout: 5000 })
          .should('be.visible')
          .should('contain', 'Length')
          .should('contain', 'Width')
          .should('contain', 'Depth');
      });
  }

  meleeRequestButtonAssertions() {
    cy.get(this.meleeRequestButton, { timeout: 60000 }).should('be.visible').contains('Make a request').click();
  }

  verifyAutoCompleteFormInFilters(shape, color, clarity, cut) {
    cy.get(this.shape, { timeout: 5000 })
      .should('be.visible')
      .contains(shape)
      .scrollIntoView()
      .should('have.attr', 'color', '#18181B');
    cy.get(this.colorComponent, { timeout: 5000 }).should('be.visible').contains(color).scrollIntoView();
    cy.get(this.clarity, { timeout: 5000 })
      .should('be.visible')
      .contains(clarity)
      .should('have.css', 'color', 'rgb(86, 32, 225)');
    cy.get(this.cut, { timeout: 5000 })
      .should('be.visible')
      .contains(cut)
      .should('have.css', 'color', 'rgb(86, 32, 225)');
  }

  verifyMaxStonesInStepTwo() {
    cy.get(this.nextButton).contains('Next').click();
    cy.get(this.sizeInputs).should('have.length', 10);
  }
}

export default MeleeSR;
