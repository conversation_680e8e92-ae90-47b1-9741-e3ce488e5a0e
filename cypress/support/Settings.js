/// <reference types="cypress" />
const { uniqueNamesGenerator, names } = require('unique-names-generator');
const {
  UniqueString,
  UniqueNumber,
  UniqueStringId,
  UniqueNumberId,
  UniqueOTP,
  UniqueCharOTP,
  HEXColor,
  uuid
} = require('unique-string-generator');
const firstName = uniqueNamesGenerator({ dictionaries: [names] });
const lastName = uniqueNamesGenerator({ dictionaries: [names] });

class Settings {
  constructor() {
    // Locators
    this.menu = '[data-automation-id="menu-item"]';
    this.firstName = 'input[placeholder*="First"], input[placeholder*="first"]';
    this.lastName = 'input[placeholder*="Last"], input[placeholder*="last"]';
    this.email = '[data-automation-id="Email"]';
    this.mobileNO = '[data-automation-id="phone"]';
    this.currencyFlag = '.flag.us';
    this.currency = '[data-automation-id="currency"]';
    this.checkbox = '[data-automation-id="checkbox"]';
    this.locationSaveButton = '[data-automation-id="location-save-btn"]';
    this.userSaveButton = '[data-automation-id="add-user-save"]';
    this.changeLanguageSaveButton = '[data-automation-id="language-save-btn"]';
    this.saveButton = '[data-automation-id="save_button"]';
    this.successMessage = '[class*="notification-wrapper"]';
    this.oldPassword = '[data-automation-id="current-password"]';
    this.newPassword = '[data-automation-id="new-password"]';
    this.confirmNewPassword = '[data-automation-id="confirm-password"]';
    this.profileImage = '[data-automation-id="choose-image"]';
    this.webSite = '[data-automation-id="website"]';
    this.aboutCompany = '[data-automation-id="about_your_company"]';
    this.emailNotificationButton = '[class*="sc-"]';
    this.emailNotificationField = '.form-group .form-control.input-wrapper input[type="email"]';
    this.enabledEmailNotificationButton = '[class*="sc-"]';
    this.addUserButton = '[data-automation-id="add-user"]';
    this.editUserButton = '[class*="sc-"][class*="user_edit_button"]';
    this.deleteUserButton = '[data-automation-id="delete-user"]';
    this.deleteUserConfirmDialouge = '[data-automation-id="delete-btn"]';
    this.addLocationButton = '[data-automation-id="add-loc-btn"]';
    this.locationName = '[data-automation-id="location-name"]';
    this.selectCountry = '[data-automation-id="select-country-dropdown"]';
    this.selectState = '[id="react-select-5-input"]';
    this.city = '[data-automation-id="city-input"]';
    this.postalCode = '[data-automation-id="post-code"]';
    this.addressline1 = '[data-automation-id="address-1"]';
    this.addressline2 = '[data-automation-id="address-2"]';
    this.defaultLocation = '[class*="sc-"]';
    this.editAddress = '.sc-EHOje.jiwWMl';
    this.deleteLocationButton = '[data-automation-id="shipment-delete"]';
    this.deleteshippingLocationConfirmDialouge = '[data-automation-id="delete-shipment"]';
    this.searchLocation = '[data-automation-id="location-search"]';
    this.addressline1Verification = '.locations_table';
    this.markdefaultlocation = '[data-automation-id="mark-default_location"]';
    this.passwordSaveButton = '[data-automation-id="save-btn"]';
    this.companySaveButton = '[data-automation-id="company-setting-save"]';
    this.saveEmailButton = '.save_button';
    this.toggleButtonEnabledClass = '[data-automation-id="always-enabled"]';
    this.toggleButtonDisabledClass = '[data-automation-id="always-enabled"]';
    this.emailInput = 'input[type="email"]';
    this.securityToggle = '#security-setting--switch-id';
    this.securityPopup = '.req_popup.popup';
    this.securityotp = '[data-automation-id="placeholder"]';
    this.alwaysEnabledtoggle = '[data-automation-id="always-enabled"]';
    this.disableSmsButton = '.btn.btn-wdc.deactivate_btn';
    this.businessName = '[data-automation-id="Business name"]';
    this.sortCode = '[data-automation-id="Sort code"]';
    this.accountNo = '[data-automation-id="Account Number"]';
    this.savePaymentButton = '.save_button';
    this.popupAddressVerification = '.outstanding-order-title';
    this.continueButton = '.outstanding-order-button';
    this.understandButton = '.understand_button > [class*="sc-"]';
    this.linkedPopUp = '.outstanding-order-button';
    this.dropdownToggleSelector = '.css-12jdm2r';
    this.dropdownOptionSelectorPrefix = '#react-select-3-option-';
    this.dropdownOptionTextSelector = '.sc-jAarDj';
    this.selectedCurrencyDisplaySelector = '.css-1dimb5e-singleValue';
    this.currencyBlock = 'span[style*="inline-block"]';
    this.languageDropDown = '.css-1uxv76j-singleValue';
    this.heading = '.section-heading';
    this.optionSelector = '#react-select-3-option-';
  }

  updateProfileImage(successMessage) {
    let apiUrl = Cypress.env('apiurl');

    if (apiUrl.endsWith('/graphql')) {
      apiUrl = apiUrl.slice(0, -'/graphql'.length);
    }

    cy.get(this.profileImage).selectFile('cypress/fixtures/profileimage.png');
    cy.intercept('POST', `${apiUrl}/upload/userprofile`).as('uploadUserProfile');
    cy.wait('@uploadUserProfile').its('response.statusCode').should('eq', 200);
    cy.get(this.successMessage, { timeout: 100000 }).should('have.text', `${successMessage}`);
  }
  updateComapnyLogo(successMessage) {
    let apiUrl = Cypress.env('apiurl');

    if (apiUrl.endsWith('/graphql')) {
      apiUrl = apiUrl.slice(0, -'/graphql'.length);
    }

    cy.get(this.profileImage).selectFile('cypress/fixtures/profileimage.png');
    cy.intercept(`${apiUrl}//upload/company/image`).as('uploadUserProfile');
    cy.wait('@uploadUserProfile').its('response.statusCode').should('eq', 200);
    cy.get(this.successMessage, { timeout: 100000 }).should('have.text', `${successMessage}`);
  }

  updatePersonalSettings(fixtureFileName, successMessage, updateSaveButton = true) {
    if (updateSaveButton) {
      this.userSaveButton = '[data-automation-id="save_button"]';
    }
    const firstName = uniqueNamesGenerator({ dictionaries: [names] });
    const lastName = uniqueNamesGenerator({ dictionaries: [names] });
    cy.fixture(fixtureFileName).then((user) => {
      const email = user[0].email;
      cy.get(this.firstName).clear().type(firstName);
      cy.get(this.lastName).clear().type(lastName);
      cy.get(this.mobileNO).clear().type('+923327400111');
      cy.get(this.userSaveButton).click({ force: true });
      cy.get(this.successMessage, { timeout: 60000 }).should('have.text', `${successMessage}`);
    });
  }

  changePassword() {
    cy.get(this.menu).contains('Change password').click();
    cy.get(this.oldPassword).click().type('Nivoda123');
    cy.get(this.newPassword).click().type('Nivoda123');
    cy.get(this.confirmNewPassword).click().type('Nivoda123');
    cy.get(this.passwordSaveButton).click({ force: true });
    cy.get(this.successMessage, { timeout: 60000 }).should('have.text', 'Successfully updated your password.');
  }
  openCompanySetting() {
    cy.get(this.menu).contains('Company Settings').click();
    cy.url().should('include', 'settings/company');
  }
  updateCompanySetting() {
    this.openCompanySetting();
    cy.get(this.webSite).clear({ force: true }).type('https://website-automation.nivodaapi.net/');
    cy.get(this.aboutCompany).clear().type('this is test data in the about company');
    cy.get(this.companySaveButton).click({ force: true });
    cy.get(this.successMessage, { timeout: 6000 }).should('have.text', 'Successfully updated your company.');
  }
  changeLanguage() {
    cy.get(this.menu).contains('Change language').click();
    cy.url().should('include', 'settings/language');
    cy.get(this.changeLanguageSaveButton).click();
    cy.get(this.successMessage, { timeout: 6000 }).should('have.text', 'Successfully updated the language');
  }
  openUserManagment() {
    cy.get(this.menu).contains('User Management').click();
    cy.url().should('include', 'settings/userManagement');
  }

  User(successMessage) {
    const firstName = uniqueNamesGenerator({ dictionaries: [names] });
    const lastName = uniqueNamesGenerator({ dictionaries: [names] });
    cy.get(this.firstName).clear().type(firstName);
    cy.get(this.lastName).clear().type(lastName);
    cy.get(this.email).clear().type(`${firstName}${lastName}@Nivoda.net`);
    cy.get(this.mobileNO).clear().type('+923327400111');
    cy.get(this.userSaveButton).click();
    cy.get(this.successMessage, { timeout: 60000 }).should('have.text', `${successMessage}`);
    cy.url().should('include', 'settings/userManagement');
  }
  addUser(successMessage) {
    cy.get(this.addUserButton, { timeout: 90000 }).should('be.visible');
    cy.get(this.addUserButton).click();

    this.User(`${successMessage}`);
  }
  updateUser(fixtureFileName, successMessage) {
    cy.get(this.editUserButton, { timeout: 90000 }).should('be.visible');
    cy.get(this.editUserButton).eq(0).click();
    this.updatePersonalSettings(fixtureFileName, successMessage, false);
  }
  deleteUser(successMessage) {
    const defaultTimeout = 55000;

    cy.get(this.deleteUserButton, { timeout: defaultTimeout }).eq(0).click();
    cy.get(this.deleteUserConfirmDialouge, { timeout: defaultTimeout }).click();
    cy.get(this.successMessage, { timeout: defaultTimeout }).should('have.text', `${successMessage}`);
  }
  visitShippingDestination() {
    cy.get(this.menu).contains('Shipping Destinations').click();
    cy.url().should('include', 'settings/shipping');
  }
  addNewLocation() {
    const addressLine1 = `${firstName} ${lastName} Nivoda Location Line 1`;
    const addressLine2 = `${firstName} ${lastName} Nivoda Location Line 2`;
    const defaultTimeout = 55000;

    cy.get(this.addLocationButton, { timeout: defaultTimeout }).click();
    cy.get(this.locationName, { timeout: defaultTimeout }).type('Lahore');
    cy.get(this.selectCountry, { timeout: defaultTimeout }).select('Pakistan');
    cy.get(this.selectState, { timeout: defaultTimeout }).click().type('Punjab{enter}');
    cy.get(this.city, { timeout: defaultTimeout }).type('Lahore');
    cy.get(this.postalCode, { timeout: defaultTimeout }).type('54000');
    cy.get(this.addressline1, { timeout: defaultTimeout }).clear().type(`${addressLine1}`);
    cy.get(this.addressline2, { timeout: defaultTimeout }).clear().type(`${addressLine2}`);
    cy.get(this.locationSaveButton, { timeout: defaultTimeout }).click();
    // cy.get(this.understandButton, { timeout: defaultTimeout }).click();
    // cy.get(this.popupAddressVerification, { timeout: defaultTimeout }).eq(2).click();
    cy.url().should('include', 'settings/shipping');
    return {
      addressLine1
    };
  }
  markAsDefaultLocation(successMessage) {
    const defaultTimeout = 55000;

    cy.get(this.markdefaultlocation, { timeout: defaultTimeout }).eq(0).click();
    cy.get(this.successMessage, { timeout: defaultTimeout }).should('have.text', `${successMessage}`);
    cy.wait(2000);
  }
  editLocation(successMessage) {
    const defaultTimeout = 55000;

    cy.get(this.editAddress, { timeout: defaultTimeout }).eq(0).click();
    cy.get(this.addressline1, { timeout: defaultTimeout })
      .clear()
      .type(`${firstName} ${lastName} Nivoda Location Line 1`);
    cy.get(this.locationSaveButton, { timeout: defaultTimeout }).click();
    cy.get('body').then(($body) => {
      if ($body.find(this.popupAddressVerification, { timeout: defaultTimeout }).length > 0) {
        cy.get(this.continueButton, { timeout: defaultTimeout }).click();
        cy.get(this.understandButton, { timeout: defaultTimeout }).click();
      }
    });
    cy.get('body').then(($body) => {
      if ($body.find(this.popupAddressVerification, { timeout: defaultTimeout }).length > 0) {
        cy.get(this.understandButton, { timeout: defaultTimeout }).click();
      }
    });

    cy.get(this.successMessage, { timeout: defaultTimeout }).should('have.text', `${successMessage}`);
  }
  deleteLocation(successMessage) {
    const defaultTimeout = 55000;

    cy.get(this.deleteLocationButton, { timeout: defaultTimeout }).eq(2).click();
    cy.get('body').then(($body) => {
      if ($body.find(this.linkedPopUp).length > 0) {
        cy.get(this.linkedPopUp, { timeout: defaultTimeout }).should('be.visible').click();
      } else {
        cy.contains('Are you sure you want to continue?', { timeout: defaultTimeout }).should('be.visible');
        cy.get(this.deleteshippingLocationConfirmDialouge, { timeout: defaultTimeout }).should('be.visible').click();
        cy.get(this.successMessage, { timeout: defaultTimeout }).should('be.visible').and('have.text', successMessage);
      }
    });
  }

  searchDestination() {
    const { addressLine1 } = this.addNewLocation();
    cy.get(this.searchLocation).type(`${addressLine1}`);
    cy.get(this.addressline1Verification).should('contain', addressLine1);
  }
  changeEmailNotifications(emailtype, action, successMessage) {
    cy.get(this.menu).contains('Email notifications').click();

    cy.get('.form-element', { timeout: 20000 })
      .filter((index, element) => {
        return element.innerText.includes(emailtype);
      })
      .then(($formElement) => {
        cy.wrap($formElement).as('confirmedFormElement');

        cy.get('@confirmedFormElement')
          .find(this.emailInput)
          .then(($input) => {
            const isDisabled = $input.is(':disabled');

            if (action === 'enableEmail') {
              if (isDisabled) {
                cy.get('@confirmedFormElement').find(this.toggleButtonDisabledClass).click();
                cy.wait(1000);
                cy.get('@confirmedFormElement').find(this.emailInput).should('not.have.attr', 'disabled');
                cy.get('@confirmedFormElement').find(this.toggleButtonEnabledClass).should('exist');
              }
            } else if (action === 'disableEmail') {
              if (!isDisabled) {
                cy.get('@confirmedFormElement').find(this.toggleButtonEnabledClass).click();
                cy.wait(1000);
                cy.get('@confirmedFormElement').find(this.emailInput).should('have.attr', 'disabled');
                cy.get('@confirmedFormElement').find(this.toggleButtonDisabledClass).should('exist');
              }
            }
          });
      });

    cy.get(this.saveEmailButton).click();
    cy.get(this.successMessage, { timeout: 60000 }).should('have.text', successMessage);
  }

  securityenabledisable(action, successMessage) {
    cy.contains('Security').click();

    cy.get(this.securityToggle, { timeout: 60000 }).then(($checkbox) => {
      const style = window.getComputedStyle($checkbox[0]);

      if (action === 'enableSecurity') {
        console.log('enable security');
        if (style.backgroundColor !== 'rgb(12, 10, 9)' || style.borderColor !== 'rgb(12, 10, 9)') {
          cy.get(this.securityToggle, { timeout: 60000 }).click();

          cy.get(this.securityToggle).then(($checkboxAfter) => {
            const newStyle = window.getComputedStyle($checkboxAfter[0]);
            expect(newStyle.backgroundColor).to.equal('rgb(224, 224, 224)');
            expect(newStyle.borderColor).to.equal('rgb(224, 224, 224)');
            expect(newStyle.minWidth).to.equal('48px');
          });
        }
      } else if (action === 'disableSecurity') {
        console.log('disable   security');
        if (style.backgroundColor !== 'rgb(224, 224, 224)' || style.borderColor !== 'rgb(224, 224, 224)') {
          cy.get(this.securityToggle, { timeout: 60000 }).click();
          cy.get(this.securityPopup, { timeout: 60000 }).should('be.visible');

          const expectedTimestamp = new Date();
          cy.getOTP(expectedTimestamp).then((otp) => {
            cy.get(this.securityotp).click().type(otp);
          });
          cy.get(this.disableSmsButton).click();
          cy.get(this.successMessage, { timeout: 60000 }).should('have.text', successMessage);
          cy.get(this.securityPopup, { timeout: 60000 }).should('not.exist');
          cy.wait(2000);
          cy.get(this.securityToggle).then(($checkboxAfter) => {
            const newStyle = window.getComputedStyle($checkboxAfter[0]);
            expect(newStyle.backgroundColor).to.equal('rgb(224, 224, 224)');
            expect(newStyle.borderColor).to.equal('rgb(224, 224, 224)');
          });
        }
      }
    });
  }

  invoiceCommunicationAlwaysenabled() {
    cy.get(this.alwaysEnabledtoggle).eq(10).should('have.attr', 'disabled');
  }

  updatePaymentSettings() {
    cy.contains('Payment settings').click();
    cy.get(this.businessName).clear().type('Test Business');
    cy.get(this.sortCode).clear().type('1234');
    cy.get(this.accountNo).clear().type('********');
    cy.get(this.checkbox).click();
    cy.get(this.savePaymentButton).click();
  }
  updateLanguageandVerify(language, expectedText) {
    cy.get(this.spinner, { timeout: 60000 }).should('not.exist');
    cy.get(this.languageDropDown, { timeout: 60000 }).should('be.visible').click();
    cy.contains(language).click({ force: true });
    cy.get(this.heading, { timeout: 60000 })
      .should('be.visible')
      .invoke('text')
      .should((text) => {
        expect(text.trim()).to.eq(expectedText);
      });
  }

  updateCurrencyAndVerify(index, expectedSymbol) {
    cy.get(this.dropdownToggleSelector).should('be.visible').click({ force: true });
    cy.get(this.optionSelector + `${index}`, { timeout: 10000 })
      .scrollIntoView()
      .should('be.visible')
      .click({ force: true });
    cy.contains('Currency updated successfully', { timeout: 15000 }).scrollIntoView().should('be.visible');
    cy.get(this.currencyBlock, { timeout: 10000 }).should('contain.text', expectedSymbol);
  }
}
export default Settings;
