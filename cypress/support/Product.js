/// <reference types="cypress" />
import moment from 'moment-timezone';
import { faker } from '@faker-js/faker';

class Product {
  constructor() {
    // Locators
    this.resultText = '[data-automation-id="section-result"]';
    this.holdButton = '[data-automation-id="request-hold-btn"]';
    this.holdButtonText = '[data-automation-d="place-hold-request"]';
    this.searchBar = '[data-automation-id="search-by-certificate-number-or-stockId"]';
    this.certNo = '[data-automation-id="cert-number"]';
    this.addToCartButton = '[data-automation-id="add-to-cart"]';
    this.addingToCartLoader = '[data-automation-id="adding-to-cart-loader"]';
    this.viewCartButton = '[data-automation-id="view-cart-btn"]';
    this.shortlistButton = '[data-automation-id="shortlist-btn"]';
    this.shortlistButtonText = '[data-automation-id="add-to-shortlist"]';
    this.addMeleeToShortList = '[data-automation-id="add-to-cart-btn"]';
    this.searchBarText = '[data-automation-id="search-by-certificate-number-or-stockIdtext"]';
    this.meleeShortlistButton = '[data-automation-id="melee_action__shortlist"]';
    this.meleeCartInput = '[data-automation-id="select-quantity-modal__input-field"]';
    this.shortlistSuccessModal = '[data-automation-id="notification_msg"]';
    this.meleeShortlistSuccessModal = '.go3958317564';
    this.holdConfirmButton = '[data-automation-id="hold-request-confirm"]';
    this.notListedText = '[data-automation-id="not-available-text"]';
    this.selectCertificateType = '[data-automation-id="arrow-icon"]';
    this.requestButton = '[data-automation-id="request-button"]';
    this.sucessRequestTicker = '[data-automation-id="request-successfull"]';
    this.checkBox = '[data-automation-id="checkbox"]';
    this.pairListButton = '[data-automation-id="pairs-drawer-button"]';
    this.pairHeader = '[data-automation-id="discover-matching-pairs-header"]';
    this.productTitle = '[data-automation-id="diamond-title"]';
    this.viewAllSuggestionButton = '[data-automation-id="view-all-suggestion"]';
    this.listViewButton = '[data-testid="search-melee-list-view-btn"]';
    this.resultBody = '.list_button';
    this.moreDetailsButton = '[data-automation-id="more-details-button"]';
    this.actionSelection = '[data-automation-id="actions-section"]';
    this.pairsAddToCartButton = '[data-automation-id="matching-pairs-add-to-cart-button"]';
    this.suggestionPair = '[data-automation-id="suggestion-pairs"]';
    this.noMatchingPair = '[data-automation-id="no-matching-pair"]';
    this.shortlistIconPairPage = '[data-automation-id="shortlist-pair-icon"]';
    this.gemsTitle = '[data-automation-id="diamond-title"]';
    this.container = '[data-automation-id="product-container"]';
    this.priceItems = '.price-items';
    this.clearButton = '[data-automation-id="melee_action__selectQuantityBtn"]';
    this.verificationText = '[data-automation-id="checkout-banner"]';
    this.listViewPriceSection = '[data-automation-id="price-section"]';
    this.meleePriceSection = '[data-automation-id="price-section"]';
    this.meleeTable = '[data-automation-id="meleeTable__label"]';
    this.supplierName = '[data-automation-id="supplier-name"]';
    this.certWrapper = '[data-automation-id="cert-wrapper"]';
    this.selectcheckbox = '[data-automation-id="checkbox"]';
    this.nextButton = '[data-automation-id="next-button"]';
    this.submitRequestButton = '[data-automation-id="submit-button"]';
    this.returnSection = '[data-automation-id="no-return-option"]';
    this.meleeAddToCartButton = '[data-testid="add-to-cart-btn"]';
    this.minicartButton = '[data-automation-id="web-minicart-box"]';
    this.pairSpinner = '[data-automation-id="spinner"]';
    this.nivodaExpressBtn = '.nivoda-express-button';
    this.oneToTwoDeliveryBtn = '.show-on-web';
    this.oneToTwoDeliveryTime = '.delivery-time';
    this.svgIcon = '.sc-EHOje.ihAgJH.delivery-truck';
    this.productLocationDetails = '.product-page-location_details';
    this.pdpExpressIcon = '.sc-EHOje.ihAgJH';
    this.expressReturnText = '.return-status.emp';
    this.expressReturnIcon = '.return-text [class*="sc-"]';
    this.memoProcessToCheckoutBtn = '.mcw_cart-summary_footer';
    this.reviewCartTitle = '.review_cart_page_left__header--title';
    this.deliveryDetails = '.cart_item_basic__item_info__list--object--value';
    this.customerRefeenceField = '[data-automation-id="Customer reference (Optional)"]';
    this.orderNotesField = '.cart_item_basic__item_notes--value__osn';
    this.editQcBtn = '[style="display: flex; justify-content: space-between;"] > button';
    this.qcPopUpInputField = '.qc__popup-input';
    this.qcPopUpSaveBtn = '.btn.btn-wdc.animated_btn.section__action__btn';
    this.generalOrderNotesField = '.general_order_notes--input_wrapper__input';
    this.orderAmounts = '.order-summary__price-value span';
    this.diamondDetailBody = '.diamond-detail-body';
    this.diamondStockIdContainer = '[data-automation-id="stock-id"]';
    this.GemsStockIdContainer = '.id-box.stockId-box';
    this.infoSection = '.info__section';
    this.stockCertContainer = '.stock-cert';
    this.deliveryTimeField = '.delivery-box > .delivery-wrapper > .delivery-time';
    this.endOfSearch = '.pairs-end-of-search';
    this.scsIcon = 'img[alt="Badge"]';
    this.scsTooltip = '.scs-tooltip';
    this.scsTooltipLinkText = '.scs-tooltip-content--link .link--text';
    this.selectQuantityButton = '[data-automation-id="melee_action__selectQuantityBtn"]';
    this.makeARequestButton = '[data-testid="special_request--switchButton"]';
    this.pdpClick = 'a.diamond-title[data-automation-id="diamond-title"]';
    this.showroomRemoveCartModal = '.modal-container';
    this.showroomRemoveCartModalContent = '.remove-modal-content';
    this.removeCartButton = '.sc-EHOje.jwIoeD';
    this.removeFromCartShowroomItem = '.btn.remove';
    this.cart = '[data-automation-id="memo-wallet"]';
    this.showroomBadge = '.showroom-badge';
    this.checkoutShowroomCartItem = '.cart-item-showroom';
    this.removePLPButton = '.sc-hENMEE';
    this.showroomLogoCheckout = '.showroom-logo img[alt="showroom logo"]';
    this.showroomComment = '.showroom-comment';
    this.showroomQuotePrice = '.quoted_price';
    this.showroomProfitPrice = '.item_price';
    this.savedSearchButton = '.saved-search-button';
    this.subMenuWrapper = '.sub-menu-wrapper';
    this.saveSearchMenuItem = '.eJtZCd';
    this.searchNameInput = 'input[placeholder="Enter search name"]';
    this.saveSearchButtonWrapper = '.sc-gTyRtS.bbaKpN';
    this.saveSearchButtonWrapper1 = '.sc-gTyRtS.kTfbUU';
    this.globalVar = '[data-testid="search-by-certificate-number-or-stockId"]';
    this.inputSelector = 'input[data-automation-id="search-by-certificate-number-or-stockIdtext"]';
    this.noteAddToCart = '[data-automation-id="add-to-cart"]';
    this.noteTextBox = 'input[data-automation-id="Customer reference"]';
    this.editIcon = '.sc-itybZL.kEUInG';
    this.buyButton = '.enbPPn';
  }

  searchStone(fixtureFileName) {
    return cy.readFixtureFile(fixtureFileName).then((certificateData) => {
      const certificateno = certificateData[0].certNumber;
      const apiUrl = Cypress.env('apiurl');

      cy.intercept('POST', apiUrl, (req) => {
        if (
          req.body.operationName === 'platformNaturalSearchQuery' ||
          req.body.operationName === 'platformGemstoneSearchQuery' ||
          req.body.operationName === 'platformLabgrownSearchQuery'
        ) {
          req.alias = 'getproduct';
        }
      });

      return cy
        .get(this.searchBar, { timeout: 80000 })
        .should('be.visible')
        .then(() => {
          cy.get(this.searchBarText).eq(1).clear({ force: true }).click({ force: true }).type(certificateno);
          cy.wait('@getproduct').then((interception) => {
            expect(interception.response.statusCode).to.eq(200);
          });
          cy.get(this.searchBarText).eq(1).invoke('val').should('eq', certificateno);
        })
        .then(() => {
          console.log('Search Stone Certificate', certificateno);
          return certificateno;
        });
    });
  }

  pdpView() {
    cy.get(this.pdpClick).eq(1).click();
  }

  clickOnGemstone(gemstoneName) {
    cy.contains('div[data-automation-id="diamond-title"] a', gemstoneName).should('be.visible').click();
  }

  holdButtonVerify(suppliertext, fixtureFileName) {
    this.searchStone(fixtureFileName).then(() => {
      cy.get(this.resultText, { timeout: 80000 }).should('have.text', '1 result');

      cy.get(this.holdButton, { timeout: 80000 }).eq(0).should('be.visible');
      cy.get(this.holdButton).eq(0).trigger('mouseover');
      cy.get(this.holdButtonText).should('have.text', suppliertext);
    });
  }
  VerifyMorCustomerCertificateNo(fixtureFileName) {
    this.searchStone(fixtureFileName).then((certificateno) => {
      cy.get(this.resultText, { timeout: 80000 }).should('have.text', '1 result');
      cy.get(this.holdButton, { timeout: 80000 }).should('be.visible');
      cy.get(this.certNo).should('have.text', `${certificateno}`);
    });
  }
  VerifyNonMorCustomerCertificateNo(fixtureFileName) {
    this.searchStone(fixtureFileName).then((certificateno) => {
      cy.log('string', certificateno);
      cy.get(this.resultText, { timeout: 80000 }).should('have.text', '1 result');
      cy.get(this.certNo, { timeout: 80000 }).should('be.visible');
      cy.get(this.certNo)
        .first()
        .then((text) => {
          const visiblePart = certificateno.slice(0, 2);
          const maskedPart = certificateno.slice(2).replace(/./g, '*');
          const maskedCertNo = visiblePart + maskedPart;
          expect(text).to.contain(maskedCertNo);
        });
    });
  }
  addProductToCart(fixtureFileName) {
    this.searchStone(fixtureFileName).then(() => {
      cy.get(this.resultText, { timeout: 80000 }).should('have.text', '1 result');
      cy.get(this.addToCartButton, { timeout: 80000 }).eq(0).scrollIntoView().should('be.visible').click();
    });
  }

  addMemoProductToCart(fixtureFileName) {
    this.searchStone(fixtureFileName).then(() => {
      cy.get(this.resultText, { timeout: 80000 }).should('have.text', '1 result');
      cy.get(this.addToCartButton, { timeout: 800000 })
        .eq(0)
        .scrollIntoView()
        .should('be.visible')
        .click({ force: true });
    });
  }
  memoProceedToCheckout() {
    cy.get(this.memoProcessToCheckoutBtn, { timeout: 80000 })
      .should('be.visible')
      .then(($el) => {
        cy.containsTranslation('proceed_to_checkout', { withinSubject: $el }).click();
      });
    cy.get(this.reviewCartTitle, { timeout: 80000 }).should('be.visible').should('contain', 'Review cart & add notes');
  }

  orderTotalCalculations() {
    cy.get(this.orderAmounts, { timeout: 50000 }).then(($els) => {
      const amounts = [...$els].slice(1, 6, 2).map((el) => parseFloat(el.innerText.replace(/[^0-9.]/g, '')) || 0);
      cy.log(`Parsed Amounts: ${amounts}`);
      const calculatedTotal = amounts[0] + amounts[1];
      cy.log(`Calculated Total: ${calculatedTotal}`);
      expect(calculatedTotal).to.be.closeTo(amounts[2], 0.01);
    });
  }

  cartAndNotesAssertions(fixtureFileName1, index, index2, index3) {
    cy.readFixtureFile(fixtureFileName1).then((certificateData) => {
      const defaultTimeout = 80000;
      const certificateno = certificateData[0].certNumber;
      cy.get(this.certNo, { timeout: defaultTimeout })
        .eq(index)
        .should('be.visible')
        .should('have.text', certificateno);
      cy.get(this.deliveryDetails, { timeout: defaultTimeout }).eq(index3).should('be.visible');
      cy.get(this.deliveryDetails, { timeout: defaultTimeout }).eq(index3).contains('1-2 Business days');
      cy.get(this.deliveryDetails, { timeout: defaultTimeout })
        .eq(index2)
        .should('be.visible')
        .then(($el) => {
          cy.containsTranslation('returnable', { withinSubject: $el });
        });
      cy.get(this.generalOrderNotesField, { timeout: defaultTimeout })
        .should('be.visible')
        .type('Should be QC passed', { delay: 0 });
      cy.get(this.customerRefeenceField, { timeout: defaultTimeout })
        .eq(index)
        .should('be.visible')
        .type('Reference No:1', { delay: 0 });
      cy.get(this.orderNotesField, { timeout: defaultTimeout })
        .eq(index)
        .should('be.visible')
        .type('Order Note 1', { delay: 0 });
      cy.get(this.editQcBtn, { timeout: defaultTimeout }).should('be.visible').click();
      cy.get(this.qcPopUpInputField, { timeout: defaultTimeout })
        .should('be.visible')
        .clear()
        .type('Should be QC passed', { delay: 0 });
      cy.get(this.qcPopUpSaveBtn, { timeout: defaultTimeout })
        .should('be.visible')
        .should('be.enabled')
        .then(($el) => {
          cy.containsTranslation('save', { withinSubject: $el }).click();
        });
    });
  }

  shortlistButtonVerify(fixtureFileName) {
    this.searchStone(fixtureFileName).then(() => {
      cy.get(this.resultText, { timeout: 80000 }).should('have.text', '1 result');
      cy.get(this.shortlistButton, { timeout: 80000 }).eq(0).should('be.visible');
      cy.get(this.shortlistButton).first().trigger('mouseover');
      cy.get(this.shortlistButtonText, { timeout: 80000 }).should('have.text', 'Add to Shortlist');
      cy.get(this.shortlistButton).eq(0).click();
    });
  }

  shortlistStonePDP() {
    cy.get(this.shortlistButton, { timeout: 80000 }).eq(0).should('be.visible').trigger('mouseover');
    cy.get('body').then(($body) => {
      if ($body.find(this.shortlistButtonText).text().trim() === 'Add to Shortlist') {
        cy.get(this.shortlistButton).eq(0).click();
      } else {
        cy.get(this.shortlistButton).eq(0).dblclick().click();
      }
    });
  }

  shortlistButtonVerifyMelee(fixtureFileName) {
    cy.get(this.meleeShortlistButton, { timeout: 80000 }).should('be.visible');
    cy.get(this.meleeShortlistButton, { timeout: 80000 }).eq(0).click();

    cy.get(this.meleeCartInput, { timeout: 80000 })
      .should('be.visible')
      .clear({ force: true })
      .click({ force: true })
      .type('2', { force: true });
    cy.get(this.addMeleeToShortList, { timeout: 80000 }).should('be.visible').eq(0).click();

    cy.get(this.meleeShortlistSuccessModal, { timeout: 80000 })
      .should('be.visible')
      .then(($el) => {
        cy.containsTranslation('added_to_your_shortlists', { withinSubject: $el });
      });
  }

  shortlistMelee(index) {
    cy.get(this.meleeShortlistButton, { timeout: 80000 }).should('be.visible');
    cy.get(this.meleeShortlistButton, { timeout: 80000 }).eq(index).click();

    cy.get(this.meleeCartInput, { timeout: 80000 })
      .should('be.visible')
      .clear({ force: true })
      .click({ force: true })
      .type('2', { force: true });
    cy.get(this.addMeleeToShortList, { timeout: 80000 }).should('be.visible').eq(0).click();

    cy.get(this.meleeShortlistSuccessModal, { timeout: 80000 })
      .should('be.visible')
      .contains('Melee has been added to the shortlist');
  }

  putProductOnHold(suppliertext, fixtureFileName) {
    this.holdButtonVerify(suppliertext, fixtureFileName);
    cy.get(this.holdButton, { timeout: 80000 }).eq(0).click();
    cy.get(this.holdConfirmButton, { timeout: 80000 }).should('be.visible').click();
    cy.get(this.holdButton).eq(0).trigger('mouseover');
    cy.get(this.holdButtonText).should('have.text', 'Requested a Hold Until');
  }

  diamondRequest(fixtureFileName, requestType, requestDialog) {
    this.searchStone(fixtureFileName).then(() => {
      cy.get(this.resultText, { timeout: 180000 })
        .should('be.visible')
        .then(($el) => {
          cy.containsTranslation('results', { withinSubject: $el }).then(() => {
            cy.wrap($el).should('contain.text', '0');
          });
        });
      cy.get(this.notListedText, { timeout: 180000 }).then(($el) => {
        cy.containsTranslation('no_result_melee_cx', { withinSubject: $el });
      });
      cy.get(this.requestButton).click();
      cy.contains(`${requestType}`).click();
      cy.get(this.nextButton).click();
      if (requestType === 'Add to stock & request info') {
        cy.contains('Your request*');
        cy.get(this.checkBox).eq(0).click();
        cy.get(this.checkBox).eq(4).click();
      }
      cy.get(this.selectCertificateType).eq(2).click({ force: true });
      cy.contains('GIA').click({ force: true });
      cy.get(this.nextButton).click();
      cy.get(this.submitRequestButton).should('be.disabled');
      cy.get(this.checkBox).click();
      cy.get(this.submitRequestButton).should('be.enabled').click();
      cy.get(this.sucessRequestTicker).should('have.text', `${requestDialog}`);
      cy.get(this.sucessRequestTicker, { timeout: 80000 }).should('not.be.visible');
    });
  }
  requestNotApplicable(fixtureFileName, searchMessage) {
    cy.url().then((url) => {
      if (url.includes('gemstones')) {
        cy.get(this.shortlistButton, { timeout: 80000 }).should('be.visible');
      } else if (url.includes('melee')) {
        cy.get(this.meleeShortlistButton, { timeout: 80000 }).should('be.visible');
      }
    });

    this.searchStone(fixtureFileName).then(() => {
      cy.get(this.requestButton).should('not.exist');
      cy.get(this.resultText, { timeout: 80000 })
        .should('be.visible')
        .then(($el) => {
          cy.containsTranslation('results', { withinSubject: $el }).then(() => {
            cy.wrap($el).should('contain.text', '0');
          });
        });
      cy.contains(`${searchMessage}`);
    });
  }

  verifyPairHeader() {
    cy.get(this.pairHeader, { timeout: 80000 }).should('be.visible').contains('Discover matching pairs by Nivoda');
  }

  accessStoneSearchResult(fixtureFileName) {
    return this.searchStone(fixtureFileName).then(() => {
      cy.wait(3000);
      cy.get(this.resultText, { timeout: 80000 })
        .should('be.visible')
        .then(($el) => {
          cy.containsTranslation('result', { withinSubject: $el }).then(() => {
            cy.wrap($el).should('contain.text', '1');
          });
        });
    });
  }

  accessPairListFromIcon(fixtureFileName) {
    this.accessStoneSearchResult(fixtureFileName).then(() => {
      cy.get(this.pairListButton, { timeout: 80000 }).should('be.visible').click();
      this.verifyPairHeader();
    });
  }

  accessDiamondProductDetails() {
    cy.get(this.productTitle, { timeout: 80000 }).eq(0).should('be.visible').click();
  }

  accessGemsProductDetails() {
    cy.get(this.gemsTitle, { timeout: 80000 }).eq(0).should('be.visible').click();
  }

  viewAllSuggestions() {
    cy.get(this.viewAllSuggestionButton, { timeout: 80000 }).then(($el) => {
      cy.containsTranslation('view_all_suggestions', { withinSubject: $el })
        .scrollIntoView()
        .should('be.visible')
        .click();
    });
    this.verifyPairHeader();
  }

  accessPairListFromDetails(fixtureFileName) {
    this.accessStoneSearchResult(fixtureFileName).then(() => {
      this.accessDiamondProductDetails();
      cy.get(this.suggestionPair, { timeout: 80000 }).then(($element) => {
        if ($element.text().includes('No matching pairs found')) {
          cy.log('No matching pairs found');
          return;
        } else {
          this.viewAllSuggestions();
        }
      });
    });
  }

  accessPairListFromListView(fixtureFileName) {
    this.accessStoneSearchResult(fixtureFileName).then(() => {
      cy.get(this.listViewButton, { timeout: 80000 }).should('be.visible').click();
      cy.get(this.resultBody, { timeout: 80000 }).should('be.visible').click();
      cy.get(this.moreDetailsButton, { timeout: 80000 }).should('be.visible').click();
      cy.wait(15000);
      cy.get(this.suggestionPair, { timeout: 80000 }).then(($element) => {
        if ($element.text().includes('No matching pairs found')) {
          cy.log('No matching pairs found');
          return;
        } else {
          this.viewAllSuggestions();
        }
      });
    });
  }

  addToCartFromPairDetails(fixtureFileName) {
    this.accessStoneSearchResult(fixtureFileName).then(() => {
      this.accessDiamondProductDetails();
      cy.wait(15000);
      cy.get(this.suggestionPair, { timeout: 80000 }).then(($element) => {
        if (!$element.text().includes('No matching pairs found')) {
          this.viewAllSuggestions();
          cy.get(this.pairsAddToCartButton, { timeout: 80000 })
            .eq(1)
            .should('be.visible')
            .then(($el) => {
              cy.containsTranslation('add_to_cart', { withinSubject: $el }).click();
            });
        }
      });
    });
  }

  addToCartFromPairIcon(fixtureFileName) {
    this.accessPairListFromIcon(fixtureFileName);
    cy.get(this.pairsAddToCartButton, { timeout: 80000 })
      .eq(0)
      .should('be.visible')
      .then(($el) => {
        cy.containsTranslation('add_to_cart', { withinSubject: $el }).click();
      });
  }

  addToShortlistFromPairIcon(fixtureFileName) {
    this.accessPairListFromIcon(fixtureFileName);
    cy.get(this.shortlistIconPairPage, { timeout: 80000 }).eq(0).should('be.visible').click();
  }

  addToShortlistFromPairDetails(fixtureFileName) {
    this.accessStoneSearchResult(fixtureFileName).then(() => {
      this.accessDiamondProductDetails();
      cy.wait(15000);
      cy.get(this.suggestionPair, { timeout: 80000 }).then(($element) => {
        if (!$element.text().includes('No matching pairs found')) {
          this.viewAllSuggestions();
          cy.get(this.shortlistIconPairPage, { timeout: 80000 }).eq(1).should('be.visible').click();
        }
      });
    });
  }

  matchingPairNotVisibleForGemstoneOnDetailsPage(fixtureFileName) {
    this.accessStoneSearchResult(fixtureFileName).then(() => {
      this.accessGemsProductDetails(fixtureFileName);
      cy.wait(3000);
      cy.get(this.suggestionPair, { timeout: 80000 }).should('not.exist');
    });
  }

  matchingPairIconNotVisibleForGemstone(fixtureFileName) {
    this.searchStone(fixtureFileName).then(() => {
      cy.get(this.resultText, { timeout: 80000 }).should('have.text', '1 result');
      cy.get(this.pairListButton, { timeout: 80000 }).should('not.exist');
    });
  }

  priceNotVisibleForUnVerifiedUsers() {
    cy.url().then((url) => {
      const container = url.includes('diamond')
        ? this.container
        : url.includes('gemstones')
          ? this.container
          : this.container;

      cy.get(container, { timeout: 80000 }).eq(0).should('be.visible');
      cy.wait(3000);
      url.includes('melee')
        ? cy.get(this.meleePriceSection, { timeout: 80000 }).then(($el) => {
            cy.containsTranslation('hidden', { withinSubject: $el });
          })
        : cy.get(this.priceItems).should('not.exist');
    });
  }

  addMultipleStonesToCartFromPairIcon(fixtureFileName) {
    this.addToCartFromPairIcon(fixtureFileName);
    cy.wait(15000);

    cy.get('body').then(($body) => {
      if ($body.find(this.endOfSearch, { timeout: 6000 }).length > 0) {
        cy.log('No matching pairs found. Ending test.');
        return;
      }

      cy.get(this.pairsAddToCartButton)
        .eq(1)
        .should('be.visible')
        .then(($el) => {
          cy.containsTranslation('add_to_cart', { withinSubject: $el }).click();
        });
    });
  }

  addProductToCartDisabled() {
    cy.get(this.addToCartButton, { timeout: 80000 }).should('be.visible').should('be.disabled');
  }
  accessListView() {
    cy.get(this.listViewButton, { timeout: 80000 }).should('be.visible').click();
    cy.get(this.resultBody, { timeout: 80000 }).eq(0).should('be.visible').click();
  }

  verifyVerificationText() {
    cy.get(this.verificationText, { timeout: 80000 }).should('be.visible').contains('We are verifying your account');
  }

  holdButtonNotVisibleOnStoneCard() {
    cy.get(this.container, { timeout: 50000 }).eq(0).should('be.visible');
    cy.get(this.holdButton, { timeout: 80000 }).should('not.exist');
  }

  holdButtonNotVisibleOnStoneDetails() {
    this.accessDiamondProductDetails();
    cy.get(this.holdButton, { timeout: 80000 }).should('not.exist');
  }

  holdButtonNotVisibleOnStoneListViewPage() {
    this.accessListView();
    cy.get(this.holdButton, { timeout: 80000 }).should('not.exist');
  }

  matchingPairIconNotVisibleOnStoneCard() {
    cy.get(this.container, { timeout: 50000 }).eq(0).should('be.visible');
    cy.get(this.pairIcon, { timeout: 80000 }).should('not.exist');
  }

  matchingPairIconNotVisibleOnStoneListViewPage() {
    this.accessListView();
    cy.get(this.pairIcon, { timeout: 80000 }).should('not.exist');
  }

  priceNotVisibleOnStoneDetails() {
    cy.url().then((url) => {
      url.includes('diamond') ? this.accessDiamondProductDetails() : this.accessGemsProductDetails();
      cy.get(this.priceItems, { timeout: 80000 }).should('not.exist');
    });
  }

  stockIdNotVisibleOnStoneDetails() {
    cy.url().then((url) => {
      url.includes('diamond') ? this.accessDiamondProductDetails() : this.accessGemsProductDetails();
      cy.get(this.diamondDetailBody, { timeout: 10000 }).invoke('text').should('not.be.empty');
      cy.get(this.stockCertContainer, { timeout: 80000 }).then(($el) => {
        cy.containsTranslation('hidden', { withinSubject: $el });
      });
    });
  }

  priceNotVisibleOnStoneListViewPage() {
    this.accessListView();
    cy.url().then((url) => {
      const priceContainer = url.includes('diamond') ? this.listViewPriceSection : this.priceItems;
      cy.get(priceContainer, { timeout: 80000 })
        .should('be.visible')
        .then(($el) => {
          cy.containsTranslation('hidden', { withinSubject: $el });
        });
    });
  }

  stockIdNotVisibleOnStoneListViewPage() {
    this.accessListView();
    cy.get(this.infoSection, { timeout: 5000 }).should('be.visible').should('contain.text', 'Hidden');
  }

  stockIdNotVisibleOnStoneCard() {
    cy.url().then((url) => {
      const stockIdContainer = url.includes('diamond') ? this.diamondStockIdContainer : this.GemsStockIdContainer;
      cy.get(this.container, { timeout: 80000 }).invoke('text').should('not.be.empty');
      cy.get(stockIdContainer, { timeout: 5000 }).should('not.exist');
    });
  }

  meleePriceNotVisibleOnStoneListViewPage() {
    cy.get(this.listViewButton, { timeout: 80000 }).should('be.visible').click();
    cy.get(this.meleeTable).contains('Hidden');
  }

  verifySupplierNameAndCertNotVisible() {
    cy.get(this.supplierName, { timeout: 80000 }).should('not.exist');
    cy.get(this.certification, { timeout: 80000 }).should('not.exist');
  }

  supplierNameAndCertNotVisibleForUnverifiedUsersOnStoneCard() {
    cy.get(this.container, { timeout: 80000 }).eq(0).should('be.visible');
    this.verifySupplierNameAndCertNotVisible();
  }

  supplierNameAndCertNotVisibleForUnverifiedUsersOnStoneDetails() {
    this.accessDiamondProductDetails();
    this.verifySupplierNameAndCertNotVisible();
  }

  supplierNameAndCertNotVisibleForUnverifiedUsersOnStoneListView() {
    this.accessListView();
    this.verifySupplierNameAndCertNotVisible();
  }

  viewCart() {
    cy.get(this.viewCartButton, { timeout: 80000 }).should('be.visible').wait(4000).click({ force: true });
  }

  addMeleeToCart(fixtureFileName) {
    cy.get(this.selectQuantityButton, { timeout: 80000 }).eq(0).click();
    cy.get(this.meleeCartInput, { timeout: 80000 })
      .eq(0)
      .should('be.visible')
      .clear()
      .should('have.value', '')
      .click()
      .type('1');
    cy.get(this.meleeAddToCartButton, { timeout: 80000 }).should('be.visible').eq(0).click();
    cy.wait(3000);
  }

  accessNivodaExpress() {
    cy.get(this.nivodaExpressBtn, { timeout: 80000 }).should('be.visible').click(50, 50, { force: true });
    cy.get('body').click(10, 100);
  }
  nivodaExpressButtonNotVisibleAssertion() {
    cy.get(this.nivodaExpressBtn, { timeout: 80000 }).should('not.exist');
  }
  stoneReturnableAssertion() {
    cy.get(this.expressReturnText, { timeout: 80000 })
      .eq(2)
      .scrollIntoView()
      .should('be.visible')
      .then(($el) => {
        cy.containsTranslation('returnable', { withinSubject: $el });
      });
    cy.get(this.expressReturnIcon, { timeout: 80000 }).eq(2).scrollIntoView().should('be.visible').click();
    cy.contains('Your free returns').should('be.visible');
  }
  nonReturnableAssertion() {
    cy.get(this.expressReturnText, { timeout: 80000 })
      .should('be.visible')
      .then(($el) => {
        cy.containsTranslation('not_returnable', { withinSubject: $el });
      });
  }
  assertExpressInfoPDPForExpressCustomer() {
    cy.get(this.productLocationDetails, { timeout: 80000 })
      .should('be.visible')
      .and('contain', '1-2 Business days')
      .then(($el) => {
        cy.containsTranslation('returnable', { withinSubject: $el });
      });
    cy.get(this.pdpExpressIcon, { timeout: 80000 }).should('be.visible');
  }
  assertExpressInfoPDPForMemoExpressCustomer() {
    cy.get(this.productLocationDetails, { timeout: 80000 })
      .should('be.visible')
      .invoke('text')
      .then((text) => {
        expect(text).to.include('1-2 Business days');
        expect(text).to.include('Not returnable');
      });
  }
  accessOneToTwoDeliveryButton() {
    cy.get(this.oneToTwoDeliveryBtn, { timeout: 80000 }).eq(2).should('be.visible').click();
    cy.get(this.oneToTwoDeliveryTime, { timeout: 80000 }).should('be.visible').and('contain', '1-2 Business days');
    cy.get(this.svgIcon, { timeout: 80000 }).eq(2).scrollIntoView().should('be.visible').click();
  }

  deliveryDaysAssertion(fixtureFileName, originCountry, destinationCountry) {
    cy.fixture(fixtureFileName).then((data) => {
      // Map 'R' (Rest of the World) based on the origin country
      let actualDestinationCountry = destinationCountry;

      if (destinationCountry === 'R') {
        if (originCountry === 'US' || originCountry === 'HK') {
          actualDestinationCountry = 'GB'; // R means GB
        } else if (originCountry === 'GB') {
          actualDestinationCountry = 'US'; // R means US
        } else if (originCountry === 'US') {
          actualDestinationCountry = 'US'; // R means US
        } else if (originCountry === 'AU') {
          actualDestinationCountry = 'US'; // R means US
        }
      }

      const deliveryInfo = data.find(
        (entry) => entry.origin_country === originCountry && entry.destination_country === destinationCountry
      );
      expect(deliveryInfo, `Delivery info for ${originCountry}-${destinationCountry}`).to.exist;

      let expectedDeliveryDays = deliveryInfo.delivery_days;

      // Adjust delivery days based on backend logic
      const destinationTimeZones = moment.tz.zonesForCountry(actualDestinationCountry);
      let offset = 0;

      if (destinationTimeZones && destinationTimeZones.length > 0) {
        const localTime = moment.tz(destinationTimeZones[0]);
        if (localTime.hours() > 9) {
          offset = 1;
        }
      } else {
        const utcTime = moment.utc();
        if (utcTime.hours() > 9) {
          offset = 1;
        }
      }

      if (offset > 0) {
        const [minDays, maxDays] = expectedDeliveryDays.split('-').map(Number);
        expectedDeliveryDays = `${minDays + offset}-${maxDays + offset}`;
      }

      cy.get(this.pairSpinner, { timeout: 6000 }).should('not.exist');

      cy.get(this.deliveryTimeField, { timeout: 500000 })
        .eq(0)
        .scrollIntoView()
        .should('be.visible')
        .should('have.text', `${expectedDeliveryDays} Business days`);
    });
  }
  verifyScsFilterAndContents() {
    cy.get(this.scsIcon, { timeout: 50000 }).eq(0).should('be.visible');
    cy.get(this.productTitle).filter(':visible').eq(1).click();
    cy.get(this.scsIcon, { timeout: 50000 }).should('be.visible');
    this.hoverOnScsBadgeAndVerifyTooltip();
  }

  hoverOnScsBadgeAndVerifyTooltip() {
    cy.stubWindowOpen();
    cy.contains('SCS rated').trigger('mouseover');
    cy.get(this.scsTooltip).should('be.visible');
    cy.get(this.scsTooltipLinkText).should('contain.text', 'Open SCS Certificate').should('be.visible').click();
    cy.get('@windowOpen').should('be.calledWithMatch', /nivoda-certificates\.s3\.eu-west-2\.amazonaws\.com\/SCS/);
  }

  searchNotAvailableGemStone() {
    const stockId = faker.number.int({ min: 100000000, max: 999999999 }).toString();

    cy.get(this.searchBarText, { timeout: 80000 })
      .eq(1)
      .should('be.visible')
      .should('be.enabled')
      .type(stockId, { force: true, delay: 0 });

    cy.get(this.makeARequestButton, { timeout: 80000 }).should('be.visible');
  }

  makeARequest() {
    cy.get(this.makeARequestButton, { timeout: 80000 }).should('be.visible').click();
  }
  verifyCertificateNumber(fixtureFileName) {
    this.searchStone(fixtureFileName).then((certificateno) => {
      cy.get(this.resultText, { timeout: 80000 }).should('have.text', '1 result');
      cy.get(this.certNo).should('have.text', `${certificateno}`);
    });
  }
  verifyGemstoneCertificateNumber(fixtureFileName) {
    this.searchStone(fixtureFileName).then((certificateno) => {
      cy.get(this.resultText, { timeout: 80000 }).should('have.text', '1 result');
      cy.get(this.certWrapper).should('contain.text', `${certificateno}`);
    });
  }

  savedSearch() {
    cy.get(this.savedSearchButton).then(($el) => {
      cy.containsTranslation('saved_searches', { withinSubject: $el }).should('be.visible').click({ force: true });
    });
    cy.get(this.subMenuWrapper, { timeout: 10000 })
      .find('.sc-igypuY.jMGsto')
      .then(($el) => {
        cy.containsTranslation('save_search', { withinSubject: $el }).should('be.visible').click({ force: true });
      });
    cy.get(this.searchNameInput).should('be.visible').clear().type('first search');
    cy.contains('div', 'Save').click({ force: true });
  }

  globalStoneSearch(fixtureFileName) {
    cy.fixture(fixtureFileName).then((data) => {
      const searchText = data.certificateNumber || data[0]?.certNumber;

      if (!searchText) {
        throw new Error(`No certificate number found in ${fixtureFileName}`);
      }

      cy.get(this.globalVar)
        .find(this.inputSelector)
        .first()
        .scrollIntoView()
        .should('exist')
        .click({ force: true })
        .clear({ force: true })
        .type(searchText, { force: true });
    });
  }

  customerReferenceNotes() {
    cy.get(this.noteAddToCart).first().scrollIntoView().should('be.visible').click({ force: true });
    cy.get(this.noteTextBox, { timeout: 5000 })
      .should('be.visible')
      .and('not.be.disabled')
      .click({ force: true })
      .clear()
      .type('Testing', { delay: 100 });
  }

  defaultQCNotes() {
    cy.get(this.viewCartButton, { timeout: 2000 }).scrollIntoView().should('be.visible').click({ force: true });
    cy.contains('button', 'Edit..', { timeout: 10000 }).should('exist').should('be.visible').click({ force: true });
    cy.get(this.qcPopUpInputField).should('be.visible').type('QA Team');
    cy.contains('button', 'Save').click({ force: true });
    cy.contains('QC Preference has been updated.').should('be.visible');
  }

  removeFromCartShowroomPopup(fixtureFileName) {
    cy.fixture(fixtureFileName).then((namedata) => {
      const CustomerName = namedata[0].name;
      cy.get(this.cart).click();
      cy.get(this.showroomBadge).then(($el) => {
        cy.containsTranslation('nivoda_showroom', { withinSubject: $el }).should('be.visible');
      });
      cy.get(this.removeCartButton).eq(0).click();

      cy.get(this.showroomRemoveCartModal, { timeout: 5000 }).should('be.visible');

      cy.getFormattedDate().then((formattedDate) => {
        cy.get(this.showroomRemoveCartModalContent).should(
          'contain.text',
          `This item was added by ${CustomerName} on ${formattedDate}. Removing it cannot be undone. Are you sure you want to proceed?`
        );
      });
    });

    cy.get(this.removeFromCartShowroomItem).click();
  }
  viewCheckoutShowroomOrder(fixtureFileName) {
    cy.fixture(fixtureFileName).then((namedata) => {
      const CustomerName = namedata[0].name;
      cy.get(this.cart).click();
      cy.get(this.showroomBadge).then(($el) => {
        cy.containsTranslation('nivoda_showroom', { withinSubject: $el }).should('be.visible');
      });
      this.viewCart();
      cy.get(this.showroomLogoCheckout).should('have.attr', 'src').and('include', 'showroom-icon');
      cy.getFormattedDate().then((formattedDate) => {
        cy.get(this.checkoutShowroomCartItem).should(
          'contain.text',
          `Ordered from showroom on ${formattedDate} By ${CustomerName}`
        );
      });
    });

    cy.get(this.showroomComment).should('have.text', 'Product To Nivoda Cart From Showroom');
    cy.get(this.showroomQuotePrice).should('contain.text', '$');
    cy.get(this.showroomProfitPrice).should('contain.text', '$');
  }
  verifyShortlistOptionDisabledForShowroomItems(fixtureFileName) {
    this.searchStone(fixtureFileName).then(() => {
      cy.get(this.resultText, { timeout: 80000 }).should('have.text', '1 result');
      cy.get(this.shortlistButton).should('not.exist');
    });
  }

  verifyRemoveShowroomItemFromCartPLPPage(fixtureFileName, fixtureFileName1) {
    cy.fixture(fixtureFileName1).then((namedata) => {
      const CustomerName = namedata[0].name;
      this.searchStone(fixtureFileName).then(() => {
        cy.get(this.resultText, { timeout: 80000 }).should('have.text', '1 result');
        cy.get(this.shortlistButton).should('not.exist');
        cy.get(this.removePLPButton).click();
        cy.getFormattedDate().then((formattedDate) => {
          cy.get(this.showroomRemoveCartModalContent).should(
            'contain.text',
            `This item was added by ${CustomerName} on ${formattedDate}. Removing it cannot be undone. Are you sure you want to proceed?`
          );
        });
      });
    });
  }
  clickBuyButton() {
    cy.wait(4000);
    cy.get(this.buyButton).click({ force: true });
  }
}
export default Product;
