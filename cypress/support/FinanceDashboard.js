/// <reference types="cypress" />
/// <reference types="cypress-downloadfile"/>

class FinanceDashboard {
  constructor() {
    // Locators
    this.creditNoteHeader = '[data-status="CREDIT_NOTES"]';
    this.paidTab = '[data-status="PAID"]';
    this.unPaidTab = '[data-status="UNPAID"]';
    this.payNowButton = '.sc-bwNswr.kfLUAj';
    this.detailsHeader = '.ts_details_header';
    this.creditNoteNumberHeader = '.ts_credit_note_number_header';
    this.tableBody = '.ts_table_body_row.tablerow_item';
    this.tableData = '.finances-dashboard_table-section_bold';
    this.invoiceStatus = '[data-automation-id="invoice-status"';
    this.checkBox = '[data-automation-id="checkbox"]';
    this.customDownloadButton = '.custom_notification--actions_download_button';
    this.creditLimitWidget = '[class^="sc-"]';
    this.titleWrapper = '.title_wrapper';
    this.creditLimit = '.content_wrapper > .total_unpaid_price';
    this.dueDateEmptyField = '.finances-dashboard_table-section_empty';
    this.filtersContainer = '.filters__container';
    this.sortBy = '.sort_by';
    this.sortingOptions = '.options';
    this.financeTabs = '.__custom_tab';
    this.tabCount = '.tab_count';
    this.ascendingOptions = '.options.custom';
    this.amountCell = '.ts_total_including_tax_cell > .finances-dashboard_table-section_bold';
    this.issueDateCell = '.ts_issue_date_cell > .finances-dashboard_table-section_bold-date';
    this.dueDateCell = '.ts_due_date_cell > .finances-dashboard_table-section_bold-date';
    this.paymentBreakdownTitle = '.payment_breakdown';
    this.paymentBreakdownItem = '.payment_breakdown__content--item__value';
    this.invoiceSummary = '.invoice_summary';
    this.invoiceDownloadBtn = '.invoice_summary > a';
    this.invoiceSummaryItems = '.invoice_summary__content--item__value';
    this.historyTitle = '.history--title';
    this.historyBody = '.history--body';
    this.stoneDescriptionTitle = '.description_title';
    this.stoneContainer = '.diamond-detail-container';
    this.searchInput = '[data-automation-id="input_box_filters__search"]';
    this.tablerow = '.ts_table_body_row.tablerow_item';
    this.invoiceTable = '.tsw_table_wrp';
    this.paymentGateway = '.content-area';
    this.lateFee = '.children_wrapper.late_fee';
    this.totalOverdue = '.total_unpaid_price';
    this.currencyModal = ':nth-child(2) > .widget_footer > :nth-child(3) > :nth-child(2)';
    this.filters = '.filters__container--left';
    this.datePickerMonth = '.react-datepicker__current-month';
    this.prevMonthIcon = '.react-datepicker__navigation.react-datepicker__navigation--previous';
    this.startDate = `.react-datepicker__day--tue`;
    this.currentMonth = '.react-datepicker__current-month';
    this.nextMonthIcon = '.react-datepicker__navigation--next';
    this.endDate = '.react-datepicker__day--wed';
    this.filterApplyBtn = '.dropdown-footer > [data-automation-id="apply-filter-btn"]';
    this.helpText = '.children_wrapper.alignCenterly';
    this.tableBodyy = '#ts_table_body';
    this.totalOverdueTitle = '.total_unpaid_title';
  }

  visitCreditNoteHeader() {
    cy.get(this.creditNoteHeader, { timeout: 60000 }).should('be.visible').contains('Credit notes').click();
  }

  visitPaidTab() {
    cy.get(this.paidTab, { timeout: 60000 }).should('be.visible').contains('Paid').click();
  }

  visitUnPaidTab() {
    cy.get(this.unPaidTab, { timeout: 60000 }).should('be.visible').contains('Unpaid').click();
  }

  dueDateDisabledInSorting() {
    cy.get(this.sortBy, { timeout: 60000 }).should('be.visible').click();
    cy.get(this.sortingOptions, { timeout: 60000 }).should('be.visible').contains('Due date').should('be.disabled');
  }

  payNowButtonNotVisibleAssertion() {
    cy.get(this.creditNoteNumberHeader, { timeout: 60000 })
      .should('be.visible')
      .should('have.text', 'Credit note number');
    cy.get(this.payNowButton).should('not.exist');
  }

  statusFilterNotVisibleAssertion() {
    cy.get(this.filtersContainer, { timeout: 60000 }).should('be.visible').and('not.contain', 'Status');
  }

  verifyCreditNoteListingData(fixtureFileName) {
    return cy.fixture(fixtureFileName).then((invoiceData) => {
      const invoiceno = invoiceData[0].invoice_number;
      const invoiceTotal = invoiceData[0].usd_total;
      this.searchInvoice(fixtureFileName);

      cy.get(this.tableData, { timeout: 10000 }).contains(invoiceno);
      cy.get(this.tableData, { timeout: 10000 }).contains(invoiceTotal);
    });
  }

  verifyAllTabistingData(fixtureFileName) {
    return cy.fixture(fixtureFileName).then((invoiceData) => {
      const invoiceno = invoiceData[0].invoice_number;
      const invoiceTotal = invoiceData[0].usd_total;
      const settledAmount = invoiceData[0].settled_amount;
      const invoiceStatus = invoiceData[0].status;
      this.searchInvoice(fixtureFileName);
      cy.get(this.tableData, { timeout: 10000 }).contains(invoiceno);
      cy.get(this.tableData, { timeout: 10000 }).contains(invoiceTotal);
      cy.get(this.tableData, { timeout: 10000 }).contains(settledAmount);
      cy.get(this.invoiceStatus, { timeout: 10000 }).contains(invoiceStatus);
    });
  }

  verifyUnpaidTabData(fixtureFileName) {
    return cy.fixture(fixtureFileName).then((invoiceData) => {
      cy.get(this.tableBodyy, { timeout: 10000 })
        .find('tr')
        .then((rows) => {
          expect(rows.length).to.be.gte(invoiceData.length);
          invoiceData.forEach((data) => {
            const { invoice_number, usd_total } = data;
            cy.contains(invoice_number);
            cy.contains(usd_total);
          });
        });
    });
  }

  verifyOverdueInvoiceData(fixtureFileName) {
    return cy.fixture(fixtureFileName).then((invoiceData) => {
      const invoiceno = invoiceData[0].invoice_number;
      const invoiceTotal = invoiceData[0].usd_total;
      const settledAmount = invoiceData[0].settled_amount;
      this.searchInvoice(fixtureFileName);

      cy.get(this.tableData, { timeout: 10000 }).contains(invoiceno);
      cy.get(this.tableData, { timeout: 10000 }).contains(invoiceTotal);
      cy.get(this.tableData, { timeout: 10000 }).contains(settledAmount);
    });
  }

  bulkDownloadInvoices() {
    cy.get(this.checkBox, { timeout: 7000 }).eq(13).should('be.visible').click();
    cy.get(this.customDownloadButton, { timeout: 10000 }).should('be.visible').click();
  }

  creditLimitWidgetNotVisibleAssertion() {
    cy.get(this.creditLimitWidget, { timeout: 10000 }).contains('Available credit limit').should('not.exist');
  }
  creditLimitWidgetVisibleAssertion(fixtureFileName) {
    cy.readFixtureFile(fixtureFileName).then((limit) => {
      const creditLimit = limit[0].Total_Account_Limit;
      cy.log('🚀 ~ FinanceDashboard ~ cy.fixture ~ creditLimit:', creditLimit);

      cy.get(this.creditLimitWidget, { timeout: 10000 }).should('be.visible');
      //cy.get(this.creditLimit, { timeout: 10000 }).should('be.visible').contains(creditLimit);
    });
  }
  totalUnpaidAndOverdueWidgetAssertion() {
    cy.get(this.titleWrapper, { timeout: 10000 }).should('be.visible').contains('Total unpaid');
    cy.get(this.titleWrapper, { timeout: 10000 }).should('be.visible').contains('Total overdue');
  }

  creditNoteDueDateAssertion() {
    cy.get(this.totalOverdueTitle, { timeout: 60000 }).eq(1).contains('Total overdue').should('be.visible');
    cy.get(this.dueDateEmptyField, { timeout: 10000 }).should('be.visible').contains('--');
  }

  invoiceTabsAssertion() {
    cy.get(this.financeTabs, { timeout: 10000 }).should('contain.text', 'Credit notes');
    cy.get(this.financeTabs, { timeout: 10000 }).should('contain.text', 'Paid');
    cy.get(this.financeTabs, { timeout: 10000 }).should('contain.text', 'Unpaid');
    cy.get(this.financeTabs, { timeout: 10000 }).should('contain.text', 'All');
    cy.get(this.tabCount, { timeout: 10000 }).should('be.visible');
  }

  sortingOptionsAssertion(a, b) {
    cy.get(this.sortBy, { timeout: 60000 }).should('be.visible').click();
    cy.get(this.sortingOptions, { timeout: 60000 }).should('be.visible').contains(a).click();
    cy.get(this.ascendingOptions, { timeout: 60000 }).should('be.visible').contains(b).click();

    let selector;
    if (a === 'Amount') {
      selector = this.amountCell;
    } else if (a === 'Date issued') {
      selector = this.issueDateCell;
    } else if (a === 'Due date') {
      selector = this.dueDateCell;
    }

    cy.get(`#row-1 > ${selector}`, { timeout: 60000 })
      .scrollIntoView()
      .should('be.visible')
      .invoke('text')
      .then((firstValueText) => {
        const firstValue =
          a === 'Amount' ? parseFloat(firstValueText.replace(/[^0-9.-]+/g, '')) : new Date(firstValueText);

        cy.get(`#row-2 > ${selector}`)
          .should('be.visible')
          .invoke('text')
          .then((secondValueText) => {
            const secondValue =
              a === 'Amount' ? parseFloat(secondValueText.replace(/[^0-9.-]+/g, '')) : new Date(secondValueText);

            if (b === 'Ascending') {
              expect(firstValue).to.be.at.most(secondValue);
            } else {
              expect(firstValue).to.be.at.least(secondValue);
            }
          });
      });
  }

  paymentBreakdownAssertion(fixtureFileName) {
    cy.fixture(fixtureFileName).then((paymentBreakdown) => {
      const invoiceNo = paymentBreakdown[0].invoice_number;
      const invoiceTotal = paymentBreakdown[0].usd_total;
      this.searchInvoice(fixtureFileName);

      cy.contains(invoiceNo, { timeout: 50000 }).should('be.visible').click();
      cy.get(this.paymentBreakdownTitle, { timeout: 50000 })
        .should('be.visible')
        .within(() => {
          cy.contains('Payment breakdown').should('be.visible');
          cy.contains('Payment terms').should('be.visible');
          cy.contains('Invoice total').should('be.visible');
          cy.contains('Amount Paid for Invoice').should('be.visible');
          cy.contains('Total due').should('be.visible');
        });
      cy.get(this.paymentBreakdownItem, { timeout: 50000 }).should('be.visible').contains(invoiceTotal);
    });
  }

  paymentBreakdownAssertionForCreditNote(fixtureFileName) {
    cy.fixture(fixtureFileName).then((paymentBreakdown) => {
      const invoiceNo = paymentBreakdown[0].invoice_number;
      const invoiceTotal = paymentBreakdown[0].usd_total;
      this.searchInvoice(fixtureFileName);

      cy.contains(invoiceNo, { timeout: 50000 }).should('be.visible').click();
      cy.get(this.paymentBreakdownTitle, { timeout: 50000 })
        .should('be.visible')
        .within(() => {
          cy.contains('Credit breakdown').should('be.visible');
          cy.contains('Total credit').should('be.visible');
          cy.contains('Total allocated').should('be.visible');
          cy.contains('Balance').should('be.visible');
        });
      cy.get(this.paymentBreakdownItem, { timeout: 50000 }).should('be.visible').contains(invoiceTotal);
    });
  }

  invoiceSummaryAssertion(fixtureFileName) {
    cy.fixture(fixtureFileName).then((paymentBreakdown) => {
      const invoiceNo = paymentBreakdown[0].invoice_number;
      this.searchInvoice(fixtureFileName);

      cy.contains(invoiceNo, { timeout: 50000 }).should('be.visible').click();
      cy.get(this.invoiceSummary, { timeout: 50000 })
        .should('be.visible')
        .within(() => {
          cy.contains('Invoice summary').should('be.visible');
          cy.contains('Status').should('be.visible');
          cy.contains('Date issued').should('be.visible');
          cy.contains('Due date').should('be.visible');
          cy.get(this.invoiceSummaryItems).should('have.length', 3);
        });
    });
  }

  invoiceSummaryAssertionForCreditNotes(fixtureFileName) {
    cy.fixture(fixtureFileName).then((paymentBreakdown) => {
      const invoiceNo = paymentBreakdown[0].invoice_number;
      this.searchInvoice(fixtureFileName);

      cy.contains(invoiceNo, { timeout: 50000 }).should('be.visible').click();
      cy.get(this.invoiceSummary, { timeout: 50000 })
        .should('be.visible')
        .within(() => {
          cy.contains('Credit note summary').should('be.visible');
          cy.contains('Status').should('be.visible');
          cy.contains('Date issued').should('be.visible');
          cy.contains('Date allocated').should('be.visible');
          cy.contains('Allocated to').should('be.visible');
          cy.get(this.invoiceSummaryItems).should('have.length', 4);
        });
    });
  }

  downloadAndViewInvoice(fixtureFileName) {
    cy.fixture(fixtureFileName).then((paymentBreakdown) => {
      const invoiceNo = paymentBreakdown[0].invoice_number;

      cy.get(this.invoiceDownloadBtn, { timeout: 60000 })
        .wait(3000)
        .should('be.visible')
        .invoke('removeAttr', 'target rel')
        .then((downloadButton) => {
          const href = downloadButton.attr('href');
          cy.downloadFile(href, 'cypress/downloads', 'invoice.pdf').then(() => {
            cy.verifyInvoicePDF(invoiceNo);
          });
        });
    });
  }

  historySectionAssertion(fixtureFileName) {
    cy.fixture(fixtureFileName).then((paymentBreakdown) => {
      const invoiceNo = paymentBreakdown[0].invoice_number;
      this.searchInvoice(fixtureFileName);
      cy.contains(invoiceNo, { timeout: 50000 }).should('be.visible').click();
      cy.get(this.historyTitle).should('be.visible').should('have.text', 'History');
      cy.get(this.historyBody, { timeout: 50000 }).should('be.visible');
    });
  }
  orderItemSectionAssertion(fixtureFileName) {
    cy.fixture(fixtureFileName).then((paymentBreakdown) => {
      const invoiceNo = paymentBreakdown[0].invoice_number;
      this.searchInvoice(fixtureFileName);

      cy.contains(invoiceNo, { timeout: 50000 }).should('be.visible').click();
      cy.get(this.stoneDescriptionTitle).eq(0).should('be.visible').click();
      cy.get(this.stoneContainer, { timeout: 50000 }).should('be.visible');
    });
  }

  searchInvoice(fixtureFileName) {
    return cy.readFixtureFile(fixtureFileName).then((invoiceData) => {
      const invoiceno = invoiceData[0]?.invoice_number || invoiceData[0]?.invoice;

      cy.log(invoiceno);
      cy.get(this.searchInput, { timeout: 60000 })
        .should('be.visible')
        .and('be.enabled')
        .clear()
        .type(invoiceno, { delay: 1000 })
        .should('have.value', invoiceno);
      cy.get(this.tablerow, { timeout: 60000 }).should('have.length', 1).contains(invoiceno, { timeout: 10000 });

      return cy.wrap(invoiceno);
    });
  }

  payInvoice() {
    cy.contains('Pay now').click();
    cy.get(this.paymentGateway).should('be.visible').contains('Select payment method');
  }

  airwallexPayInvoice() {
    cy.contains('Pay now').click();
    cy.get(this.paymentGateway).should('be.visible').contains('Payment information');
    cy.get(this.paymentGateway).should('be.visible').contains('NIVODA LIMITED');
  }

  airwallexAndStripeDisabledPayInvoice() {
    cy.contains('Pay now').click();
    cy.get(this.paymentGateway).should('be.visible').contains('Payment information');
    cy.get(this.paymentGateway).should('be.visible').contains('National Westminster Bank');
  }

  lateFeeAssertion() {
    cy.get(this.lateFee).should('be.visible').realHover();
    cy.contains(
      'The late fee for an overdue invoice will be included in the next issued invoice after the overdue invoice is paid'
    );
  }

  totalOverdueAssertion(fixtureFileName) {
    cy.fixture(fixtureFileName).then((data) => {
      const totalOverdue = parseFloat(data[0].total_overdue_amount.replace(/,/g, ''));
      cy.log(totalOverdue);
      cy.get(this.totalOverdue, { timeout: 15000 })
        .eq(1)
        .scrollIntoView()
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          const cleanedText = text.replace(/[^0-9.-]+/g, '').trim();
          const actualAmount = parseFloat(cleanedText);
          const roundedExpected = parseFloat(totalOverdue.toFixed(2));
          const roundedActual = parseFloat(actualAmount.toFixed(2));
          expect(roundedActual).to.be.closeTo(roundedExpected, 2);
        });
    });
  }

  totalOverdueCurrenciesAssertion() {
    cy.get(this.currencyModal).should('be.visible').realHover();
    cy.contains('Overdue split by currency');
  }

  totalOverdueWidgetHelpTextAssertion() {
    cy.get(this.helpText).eq(1).should('be.visible').realHover();
    cy.contains('Includes all unpaid or partially unpaid invoices which are past their due date');
  }

  dueDateFilterAssertion() {
    const today = new Date();
    const startDate = new Date(today);
    startDate.setMonth(startDate.getMonth() - 2);

    const getDayClassForDate = (date) => {
      const day = date.toLocaleString('default', { weekday: 'short' }).toLowerCase();
      return `.react-datepicker__day--${day}`;
    };

    const startDayClass = getDayClassForDate(startDate);
    const endDayClass = getDayClassForDate(today);

    cy.get(this.filters).should('be.visible').contains('Due date').click();
    cy.get(this.datePickerMonth)
      .invoke('text')
      .then((currentMonth) => {
        const startMonth = startDate.toLocaleString('default', {
          month: 'long'
        });
        const startYear = startDate.getFullYear();
        if (!currentMonth.includes(startMonth) || !currentMonth.includes(startYear)) {
          cy.get(this.prevMonthIcon).eq(1).click();
          cy.get(this.prevMonthIcon).eq(1).click();
        }
      });
    cy.get(startDayClass).should('be.visible').eq(9).click();
    cy.get(this.datePickerMonth)
      .invoke('text')
      .then((currentMonth) => {
        const currentMonthName = today.toLocaleString('default', {
          month: 'long'
        });
        cy.log(currentMonthName);
        if (currentMonth.includes(currentMonthName)) {
          cy.get(this.nextMonthIcon).eq(1).click();
          cy.get(this.nextMonthIcon).eq(1).click();
        }
      });
    cy.get(endDayClass).should('be.visible').eq(9).click();
    cy.url().then((url) => {
      const applyButtonIndex = url.includes('live/finances/all') || url.includes('live/finances/unpaid') ? 4 : 3;
      cy.get(this.filterApplyBtn, { timeout: 50000 }).eq(applyButtonIndex).should('be.enabled').click();
    });
    cy.get(this.tableBody).should('be.visible');
  }

  issueDateFilterAssertion() {
    const today = new Date();
    const startDate = new Date(today);
    startDate.setMonth(startDate.getMonth() - 2);

    const getDayClassForDate = (date) => {
      const day = date.toLocaleString('default', { weekday: 'short' }).toLowerCase();
      return `.react-datepicker__day--${day}`;
    };

    const startDayClass = getDayClassForDate(startDate);
    const endDayClass = getDayClassForDate(today);

    cy.get(this.filters).should('be.visible').contains('Issue date').click();
    cy.get(this.datePickerMonth)
      .invoke('text')
      .then((currentMonth) => {
        const startMonth = startDate.toLocaleString('default', {
          month: 'long'
        });
        const startYear = startDate.getFullYear();
        if (!currentMonth.includes(startMonth) || !currentMonth.includes(startYear)) {
          cy.get(this.prevMonthIcon).eq(0).click();
          cy.get(this.prevMonthIcon).eq(0).click();
        }
      });
    cy.get(startDayClass).should('be.visible').eq(4).click();
    cy.get(this.datePickerMonth)
      .invoke('text')
      .then((currentMonth) => {
        const currentMonthName = today.toLocaleString('default', {
          month: 'long'
        });
        cy.log(currentMonthName);
        if (!currentMonth.includes(currentMonthName)) {
          cy.get(this.nextMonthIcon).click();
          cy.get(this.nextMonthIcon).click();
        }
      });
    cy.get(endDayClass).should('be.visible').eq(4).click();
    cy.get(this.filterApplyBtn).eq(2).should('be.enabled').contains('Apply').click();
    cy.get(this.tableBody).should('be.visible');
  }
  invoiceStatusAssertion(text) {
    cy.contains(text);
  }
  invoiceStatusCheck(text) {
    cy.get(this.invoiceStatus, { timeout: 10000 }).contains(text);
  }
}

export default FinanceDashboard;
