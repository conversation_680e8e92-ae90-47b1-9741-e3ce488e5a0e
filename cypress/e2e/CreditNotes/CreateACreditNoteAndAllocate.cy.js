import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Shipment from '../../support/Admin/Shipments';
import Admin from '../../support/Admin';
import Returns from '../../support/Admin/Returns';
import Accounting from '../../support/Admin/Accounting';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('CreateCreditNoteAndPartiallyAllocate', { tags: ["@CreditNote", "@OTP", "@Failed"] }, () => {
  const login = new Login();
  const product = new Product();
  const checkout = new Checkout();
  const accessAdminTabs = new AccessAdminTabs();
  const shipment = new Shipment();
  const admin = new Admin();
  const returns = new Returns();
  const accounting = new Accounting();
  const menu = new Menu();
  const financeDashboard = new FinanceDashboard();

  it('Verify User Can Create A Credit Note', () => {
    cy.getcreditnotestone();
    cy.getMultipleAddressUser();

    login.loginUsingApi('multipleaddressuser.json');
    product.addProductToCart('cypress/fixtures/creditnotestone.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
  });
  it('Verify User Can Create A Credit Note', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Orders', 'Purchased', 'Purchase order', 'orders/purchase-order');
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/creditnotestone.json',
      'cypress/fixtures/orderNumber.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
  });

  it('Verify User Can Create Invoice Of CreditNote', () => {
    cy.clearAllSessionStorage();
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Orders', 'Purchased', 'Purchase order', 'orders/purchase-order');
    admin.accessRtcTab();
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/creditnotestone.json',
      'cypress/fixtures/orderNumber.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    cy.wait(4000);
    admin.markStoneQcPass('orderNumber.json');
    admin.accessAccounting('To Invoice');
    admin.createInvoice('cypress/fixtures/multipleaddressuser.json');
  });

  it('Verify User Can Create A Shipment And Return The Stone', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Shipments', 'Create shipment', '', 'create/customer-shipments');
    shipment.createShipmentAndVerifyAddress(
      'BDB Mumbai Office IN',
      'NYC Office US',
      'cypress/fixtures/multipleaddressuser.json',
      'orderNumber.json'
    );
    accessAdminTabs.accessTabs('Returns', 'Initiate return', '', 'returns/initiate');
    returns.initiateReturn(
      'cypress/fixtures/orderNumber.json',
      'cypress/fixtures/multipleaddressuser.json',
      'cypress/fixtures/creditnotestone.json'
    );
    accessAdminTabs.accessTabs('Accounting', 'Awaiting Payment', 'Credit Notes', 'accounting/customer-returns');
    accounting.createCreditNote('cypress/fixtures/multipleaddressuser.json', 'cypress/fixtures/orderNumber.json');
  });

  it('Verify User Can Partially Allocate The Credit Note (NE-TC-2046)', () => {
    cy.getLatestInvoice();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Accounting', 'Awaiting Payment', 'Awaiting Payment', 'accounting/awaiting-payment');
    cy.wait(5000);
    admin.searchInvoiceInAwaitingPaymentTab('cypress/fixtures/LatestInvoice.json');
    accounting.allocateCreditNote('cypress/fixtures/LatestInvoice.json');
    admin.payInvoice('cypress/fixtures/LatestInvoice.json');
  });

  it('Verify Available Credit Values Are Updated In Finance Dashboard (NE-TC-2046)', () => {
    cy.getMultipleAddressUser();
    cy.getLatestInvoice();

    login.loginUsingApi('multipleaddressuser.json');
    menu.visitFinancesPage();
    financeDashboard.totalUnpaidAndOverdueWidgetAssertion();
    financeDashboard.creditLimitWidgetVisibleAssertion('cypress/fixtures/multipleaddressuser.json');
    financeDashboard.searchInvoice('LatestInvoice.json');
    financeDashboard.invoiceStatusCheck('Allocated');
  });
});
