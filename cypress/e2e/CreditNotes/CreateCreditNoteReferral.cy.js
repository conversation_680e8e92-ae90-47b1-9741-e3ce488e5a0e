import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Accounting from '../../support/Admin/Accounting';

describe('CreateCreditNoteReferral', { tags: ['@CreditNote', '@Regression'] }, () => {
  const login = new Login();
  const accessAdminTabs = new AccessAdminTabs();
  const accounting = new Accounting();

  it('Verify User Can Create A Referral Credit Note', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Accounting', 'Credit Notes', '', 'customer-returns');
    accounting.createReferalCreditNote('cypress/fixtures/multipleaddressuser.json');
  });
});
