import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify Unverified Supplier Cannot Upload Stock', { tags: ["@Regression", "@Supplier"] }, () => {
  it('Supplier cannot upload stock with images and video (NE-TC-2622)', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('nonVerifiedSupplier.json');
    login.loginAsSupplierAssertion();
    supplier.disabledUploadButtonAssertion();
  });
});
