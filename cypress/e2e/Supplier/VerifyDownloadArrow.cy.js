import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Filter from '../../support/Filter';

describe('Verify Download Arrow NE-TC-3343', { tags: ["@Regression", "@Supplier"] }, () => {
  const login = new Login();
  const menu = new Menu();
  const filter = new Filter();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplierdashboardAnalyticsDisabled.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify arrow for Naturals', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    filter.verifyArrow();
  });

  it('Verify arrow for Labgrown', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    filter.verifyArrow();
  });

  it('Verify arrow for gems', () => {
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    filter.verifyArrow();
  });
});
