import Login from '../../../support/Login';
import Supplier from '../../../support/Supplier/Supplier';
import Menu from '../../../support/Menu';

describe('Verify Cert Search', { tags: ["@Regression", "@Supplier"] }, () => {
  const login = new Login();
  const supplier = new Supplier();
  const menu = new Menu();

  it('Verify cert search for Natural Supplier (NE-TC-2630)', () => {
    login.visitPage();
    login.loginUsingUi('NaturalDiamondSupplier.json');
    login.loginAsSupplierAssertion();
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    supplier.myStockOnly();
    supplier.certSearch('naturaldiamondcert.json');
  });

  it('Verify cert search for labgrown  (NE-TC-2629)', () => {
    cy.getlabdiamondcert();
    login.visitPage();
    login.loginUsingUi('NaturalDiamondSupplier.json');
    login.loginAsSupplierAssertion();
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    supplier.myStockOnly();
    supplier.certSearch('labdiamondcert.json');
  });

  it('Verify cert search for a gemstones  (NE-TC-3399)', () => {
    cy.getgemstonecert();
    login.visitPage();
    login.loginUsingUi('gemstonesupplier.json');
    login.loginAsSupplierAssertion();
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    supplier.myStockOnly();
    supplier.certSearch('gemstonecert.json');
  });
});
