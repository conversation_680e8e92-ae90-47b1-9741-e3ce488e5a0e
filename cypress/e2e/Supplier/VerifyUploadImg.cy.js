import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify Upload images', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Supplier should be able see upload images pop-up (NE-TC-3333)', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('loginassupplierdashboardAnalyticsDisabled.json');
    login.loginAsSupplierAssertion();
    supplier.uploadImgTab();
    supplier.verifyImguploadPopup();
  });
});
