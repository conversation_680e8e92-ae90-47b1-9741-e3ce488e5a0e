import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify DropDown values for images', { tags: ['@Regression', '@Supplier'] }, () => {
  it('Supplier should be able to see drop down status for images (NE-TC-3335)', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();

    supplier.uploadImgTab();
    supplier.selectValueDropdownOption();
  });
});
