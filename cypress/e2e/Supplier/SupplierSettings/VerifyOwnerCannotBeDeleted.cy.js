import Login from '../../../support/Login';
import Menu from '../../../support/Menu';
import Supplier from '../../../support/Supplier/Supplier';

describe('Verify owner profile deletion', { tags: ["@Regression", "@Supplier"] }, () => {
  const login = new Login();
  const menu = new Menu();
  const supplier = new Supplier();

  it('supplier should not delete owner profile NE-TC-3406', () => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    menu.visitSupplierUserPage();
    supplier.ownerCannotBeDeleted();
  });
});
