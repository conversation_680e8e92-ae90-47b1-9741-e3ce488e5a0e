import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify Filtered stone for Gemstones', { tags: ["@Regression", "@Supplier", "@Failed"] }, () => {
  it('Verify Filtered stone for Gemstones  (NE-TC-3404)', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('loginasdirectuploadsuppliergemstone.json');
    login.loginAsSupplierAssertion();
    supplier.getPreviousTime();
    supplier.gemstoneDropdown();
    supplier.uploadFile('filteredgemstone.csv', 'filteredgemstone.csv');
    supplier.getLatestTimeAndStatus();
    supplier.updateGemCertificateDelistStatus();
    supplier.getPreviousTime();
    supplier.gemstoneDropdown();
    supplier.uploadFile('filteredgemstone.csv', 'filteredgemstone.csv');
    supplier.getLatestTimeAndStatus();
    supplier.filteredStonesCheck();
  });
});
