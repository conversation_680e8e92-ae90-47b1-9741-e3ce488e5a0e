import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify Invalid for Gemstones', { tags: ["@Regression", "@Supplier", "@Failed"] }, () => {
  it('Verify Invalid for Gemstones  (NE-TC-3405)', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('loginasdirectuploadsuppliergemstone.json');
    login.loginAsSupplierAssertion();
    supplier.getPreviousTime();
    supplier.gemstoneDropdown();
    supplier.uploadFile('invalidgemstone.csv', 'invalidgemstone.csv');
    supplier.getLatestTimeAndStatus();
    supplier.checkAndClickInTheTopandBottomTable(0, 6, 3, 0);
  });
});
