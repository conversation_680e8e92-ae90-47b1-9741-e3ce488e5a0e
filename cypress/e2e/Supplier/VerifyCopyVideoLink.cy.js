import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Filter from '../../support/Filter';

describe('Verify copy Video', { tags: ["@Regression", "@Supplier"] }, () => {
  const login = new Login();
  const menu = new Menu();
  const filter = new Filter();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
  });

  it('Verify copy Video for Naturals NE-TC-3347', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    filter.verifyArrow();
    filter.copyVideoLink();
  });

  it('Verify copy Video for Labgrown NE-TC-3348', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    filter.verifyArrow();
    filter.copyVideoLink();
  });

  it('Verify copy Video for gems NE-TC-3349', () => {
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    filter.HasVideo();
    filter.verifyArrow();
    filter.copyVideoLink();
  });
});
