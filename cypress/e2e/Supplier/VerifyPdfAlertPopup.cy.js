import Login from '../../support/Login';
import Supplier from '../../support/Supplier/Supplier';

describe('Verify Popup If Uploaded File is PDF', { tags: ["@Regression", "@Supplier"] }, () => {
  it.skip('Verify an alert popup is displayed if file type other than xlsx, xls, csv is uploaded(NE-TC-2792)', () => {
    const login = new Login();
    const supplier = new Supplier();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    supplier.uploadFile('sample.pdf', 'sample.pdf');
    supplier.uploadFile30MbPopupVerify(
      'File not uploaded. Only csv, xls and xlsx files that are less than 30 MB can be uploaded.'
    );
  });
});
