import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Filter from '../../support/Filter';

describe('Verify download video', { tags: ["@Regression", "@Supplier"] }, () => {
  const login = new Login();
  const menu = new Menu();
  const filter = new Filter();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
  });

  it.only('Verify download video for Naturals NE-TC-3353', () => {
    menu.accessSupplierTabs('Natural diamonds', 'search/natural/diamond');
    filter.verifyArrow();
    filter.downloadVideo();
  });

  it('Verify download video for Labgrown NE-TC-3354', () => {
    menu.accessSupplierTabs('Lab grown diamonds', 'search/labgrown/diamond');
    filter.verifyArrow();
    filter.downloadVideo();
  });

  it('Verify download video for gems NE-TC-3355', () => {
    menu.accessSupplierTabs('Gemstones', '/search/gemstones');
    filter.HasVideo();
    filter.verifyArrow();
    filter.downloadVideo();
  });
});
