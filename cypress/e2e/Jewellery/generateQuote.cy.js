import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import CustomJewelleryQuote from '../../support/CustomJewelleryQuote';

describe('Generate Quote', () => {
  it('Verify the Request Quote functionality', () => {
    const login = new Login();
    const navbar = new Navbar();
    const customjewelleryquote = new CustomJewelleryQuote();

    login.visitPage();
    login.loginUsingUi('jewelleryUser.json');
    login.loginAsCustomerAssertion();

    navbar.visitJewelry();
    cy.contains('h1', 'Nivoda Custom Jewelry is here.', { timeout: 20000 }).should('be.visible');

    customjewelleryquote.requestQuote();
  });
});
