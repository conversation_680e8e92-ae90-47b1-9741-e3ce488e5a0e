import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import JewellerySKU from '../../support/JewelleryDashboard';

describe('Create Jewellery SKU (NE-TC-2820)', () => {
  it('Verify the Creation of new Jewellery SKU', () => {
    const login = new Login();
    const accessadmintabs = new AccessAdminTabs();
    const jewellerysku = new JewellerySKU();

    login.loginUsingAdminApi('loginasadmin.json');
    login.loginAsAdminAssertion();

    accessadmintabs.accessTabs('Jewellery Dashboard', 'Create jewellery sku', '', '/jewellery/create-jewellery-sku');
    jewellerysku.createSKU();
  });
});
