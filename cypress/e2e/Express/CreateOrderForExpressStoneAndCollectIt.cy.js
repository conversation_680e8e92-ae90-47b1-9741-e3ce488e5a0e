import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Admin from '../../support/Admin';
import Navbar from '../../support/Navbar';
import Shipment from '../../support/Admin/Shipments';

describe('CreateOrderForExpressStoneAndCollectIt', { tags: ["@OTP", "@Express", "@Failed"] }, () => {
  const login = new Login();
  const accessAdminTabs = new AccessAdminTabs();
  const product = new Product();
  const checkout = new Checkout();
  const admin = new Admin();
  const navbar = new Navbar();
  const shipment = new Shipment();

  it('Validate User Can Create Order For Express Stone And Collect it (NE-TC-1872)', () => {
    cy.getnivodaexpressstone();

    login.loginUsingApi('nivodaexpressuser.json');
    navbar.visitLabgrownDiamonds();
    product.accessNivodaExpress();
    product.addProductToCart('cypress/fixtures/nivodaexpressstone.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
  });
  it('Validate User Can Create Order For Express Stone And Collect it (NE-TC-3239)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/nivodaexpressstone.json',
      'cypress/fixtures/orderNumber.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
    admin.accessExpressRtcTab();
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/nivodaexpressstone.json',
      'cypress/fixtures/orderNumber.json',
      null,
      null,
      true
    );
  });
  it('Validate User Can Create Last Mile Shipment For Express Stone (NE-TC-1873)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Shipments', 'Create shipment', '', 'create/customer-shipments');
    shipment.createLastMileShipment('NYC Office US', 'cypress/fixtures/nivodaexpressuser.json');
  });
});
