import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import NivodaExpress from '../../support/Admin/NivodaExpress';
import Admin from '../../support/Admin';

describe('Nivoda Express Requests', { tags: ['@OTP', '@Express'] }, () => {
  it.skip('Validates Accept Request in Nivoda Express (NE-TC-1409) (NE-TC-1415) (NE-TC-1410)', () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();
    const nivodaExpress = new NivodaExpress();

    cy.getaccepttherequest();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Nivoda Express', 'Source stones', '', 'admin/nivoda-express/source-stones');
    nivodaExpress.verifSearchButton('cypress/fixtures/accepttherequest.json', 'New York', 'US customers', 'EXPRESS');
    nivodaExpress.verifyCheckBox();
    nivodaExpress.beforeSourcingStoneTextVerify('New York');
    nivodaExpress.sourceStone();
    nivodaExpress.queryExpressRequestItems('cypress/fixtures/accepttherequest.json');
    accessAdminTabs.accessTabs(
      'Nivoda Express',
      'Express requests',
      '',
      'admin/nivoda-express/express-requests/requested-stones'
    );
    nivodaExpress.reviewandConfirmRequest('cypress/fixtures/consignment.json');

    cy.wait(500000);
  });

  it.skip('Validates moving the order to QC from RTT in Nivoda Express (NE-TC-1411)', () => {
    const login = new Login();
    const admin = new Admin();

    login.loginUsingAdminApi('loginasadmin.json');
    admin.accessRttTab();
    admin.accessExpressTab();
    admin.moveToQc('cypress/fixtures/accepttherequest.json');
  });

  it.skip('Validates passing QC and verifying RTC Express in Nivoda Express (NE-TC-1412)', () => {
    const login = new Login();
    const admin = new Admin();

    login.loginUsingAdminApi('loginasadmin.json');
    admin.accessRttTab();
    admin.accessQCTab();
    admin.qcPassandVerifyRTCExpress('cypress/fixtures/accepttherequest.json');
  });
});
