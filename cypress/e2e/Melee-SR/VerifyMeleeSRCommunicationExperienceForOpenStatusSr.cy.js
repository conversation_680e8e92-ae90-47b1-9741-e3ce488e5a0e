import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Requests from '../../support/Requests';

describe('VerifyMeleeSRCommunicationExperienceForOpenStatusSR', { tags: ["@Melee-SR", "@Regression"] }, () => {
  it('Verify Customer Can Communicate with Admin related to SR in Open status (NE-TC-2478)', () => {
    const login = new Login();
    const menu = new Menu();
    const requests = new Requests();

    login.loginUsingApi('openSRCustomer.json');
    cy.getCustomerMeleeRequest();
    menu.visitRequestPage();
    requests.meleeSectionAssertion();
    requests.searchSR('customerMeleeSrdata.json');
    requests.meleeSrDetailPageAssertion();
    requests.srComment();
  });
});
