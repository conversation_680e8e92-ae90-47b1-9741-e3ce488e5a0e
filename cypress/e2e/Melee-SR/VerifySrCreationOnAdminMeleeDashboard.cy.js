import Login from '../../support/Login';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import AdminMeleeSR from '../../support/Admin/Melee-SR';
import CustomerMeleeSR from '../../support/Melee-SR';
import { skipIfPreviousTestsFailed } from 'cypress-skip-this-test';

describe('Verify SR and Order Creation Flows on Melee Dashboard', { tags: ['@Smoke', '@Melee-SR'] }, () => {
  const login = new Login();
  const accessAdminTabs = new AccessAdminTabs();
  const adminMeleeSR = new AdminMeleeSR();
  const customerMeleeSR = new CustomerMeleeSR();

  beforeEach(skipIfPreviousTestsFailed);

  beforeEach(() => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Melee Dashboard', 'Melee Requests', null, 'admin/special-requests/melee');
  });

  it('Should successfully create an SR on the Melee SR listing page (NE-TC-2132)', () => {
    adminMeleeSR.createRequestVisibilityAssertion();
    adminMeleeSR.createSrRequest('upfrontaipuser.json', 'Round', 'VVS', 'Excellent');
    customerMeleeSR.fillStep2FormForMeleeSR();
    customerMeleeSR.meleeSRFormStep3Assertions('Round', 'VVS', 'Excellent');
  });

  it('Should complete the Create Order flow for an Accepted Quotation with linked Stock ID (NE-TC-2141)', () => {
    cy.getMeleeSrData();

    adminMeleeSR.searchSR('meleeSrData.json');
    adminMeleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
    adminMeleeSR.addQuotationData('5', '5', '1', '200', '250');
    adminMeleeSR.addAndSaveModal();
    adminMeleeSR.acceptQuote();
    adminMeleeSR.createInventory();
    adminMeleeSR.makeStockLive();
    adminMeleeSR.searchSR('meleeSrData.json');
    adminMeleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
    adminMeleeSR.selectInventory();
    adminMeleeSR.acceptQuote();
    adminMeleeSR.createOrder('upfrontaipuser.json');
  });

  it('Verify Order Placed Quote status cannot be edited after order is placed (NE-TC-2150)', () => {
    adminMeleeSR.searchSR('meleeSrData.json');
    adminMeleeSR.verifyMeleeDeatilsPageThroughViewDeatils();
    adminMeleeSR.quoteStatusDisabledAssertion();
  });

  it('Verify Order number is populated on SR listing page for which order is created (NE-TC-2142)', () => {
    adminMeleeSR.searchSR('meleeSrData.json');
    adminMeleeSR.orderNumberVisibleAssertionOnMeleeSr();
  });

  it('Verify clicking on Order number on Melee SR listing page or Quotation navigates to Order Detail Page (NE-TC-2144)', () => {
    adminMeleeSR.searchSR('meleeSrData.json');
    adminMeleeSR.orderNumberNavigationAssertionOnMeleeSr();
  });
});
