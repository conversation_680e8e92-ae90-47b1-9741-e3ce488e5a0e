import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Requests from '../../support/Requests';

describe('VerifyCustomerCanCancelSR', { tags: ["@Melee-SR", "@Regression"] }, () => {
  it('Verify Customer can cancel SR (NE-TC-2288)', () => {
    const login = new Login();
    const menu = new Menu();
    const requests = new Requests();

    login.loginUsingApi('openSRCustomer.json');

    cy.getCustomerMeleeRequest();

    menu.visitRequestPage();
    requests.meleeSectionAssertion();
    requests.searchSR('customerMeleeSrdata.json');
    requests.meleeSrDetailPageAssertion();
    requests.cancelSrRequest();
  });
});
