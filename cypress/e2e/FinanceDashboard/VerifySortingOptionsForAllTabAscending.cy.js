import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifySortingOptionsForAllTabAscending (NE-TC-2045)', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const financeDashboard = new FinanceDashboard();

  beforeEach(() => {
    login.loginUsingApi('AllTabFinancesData.json');
    menu.visitFinancesPage();
  });

  it('Verify Amount Sorting in Ascending Order', () => {
    financeDashboard.sortingOptionsAssertion('Amount', 'Ascending');
  });

  it('Verify Issue Date Sorting in Ascending Order', () => {
    financeDashboard.sortingOptionsAssertion('Date issued', 'Ascending');
  });

  it('Verify Due Date Sorting in Ascending Order', () => {
    financeDashboard.sortingOptionsAssertion('Due date', 'Ascending');
  });
});
