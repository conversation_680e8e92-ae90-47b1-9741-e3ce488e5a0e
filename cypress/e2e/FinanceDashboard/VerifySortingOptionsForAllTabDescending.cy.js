import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifySortingOptionsForAllTabDescending (NE-TC-2045)', { tags: ['@FinanceDashboard', '@Regression'] }, () => {
  const login = new Login();
  const menu = new Menu();
  const financeDashboard = new FinanceDashboard();

  beforeEach(() => {
    login.loginUsingApi('AllTabFinancesData.json');
    menu.visitFinancesPage();
  });

  it('Verify Amount Sorting in Descending Order', () => {
    financeDashboard.sortingOptionsAssertion('Amount', 'Descending');
  });

  it('Verify Issue Date Sorting in Descending Order', () => {
    financeDashboard.sortingOptionsAssertion('Date issued', 'Descending');
  });

  it('Verify Due Date Sorting in Descending Order', () => {
    financeDashboard.sortingOptionsAssertion('Due date', 'Descending');
  });
});
