import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe(
  'VerifyCurrencySectionIsDisplayedForUserWithDifferentCurrencies',
  { tags: ["@FinanceDashboard", "@Regression", "@Failed"] },
  () => {
    it('Verify Currency Section Is Displayed For Invoices With Currency Different Than Users Default Currency (NE-TC-2031)', () => {
      const login = new Login();
      const menu = new Menu();
      const financeDashboard = new FinanceDashboard();

      cy.getTotalOvderDueData();

      login.loginUsingApi('totalOverdue.json');
      menu.visitFinancesPage();
      financeDashboard.totalOverdueCurrenciesAssertion();
      financeDashboard.totalOverdueAssertion('totalOverdue.json');
    });
  }
);
