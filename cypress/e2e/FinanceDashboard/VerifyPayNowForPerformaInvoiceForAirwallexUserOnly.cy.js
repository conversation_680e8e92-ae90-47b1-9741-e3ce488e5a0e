import Login from '../../support/Login';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('VerifyPayNowForPerformaInvoiceForAirWallexUserOnly', { tags: ["@FinanceDashboard", "@Regression"] }, () => {
  it.skip('Verify Airwallex Details Are Shown To Airwallex User After Clicking Pay Now For Performa Invoice (NE-TC-2035)', () => {
    const login = new Login();
    const menu = new Menu();
    const financeDashboard = new FinanceDashboard();

    login.loginUsingApi('airwallexUpfrontUser.json');
    menu.visitFinancesPage();
    financeDashboard.searchInvoice('airwallexUpfrontUser.json');
    financeDashboard.airwallexPayInvoice();
  });
});
