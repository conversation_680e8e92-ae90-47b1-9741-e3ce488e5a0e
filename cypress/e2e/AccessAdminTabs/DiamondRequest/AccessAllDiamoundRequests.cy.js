import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessAllDiamoundRequests', () => {
  it('Verify User Can Access All Diamound Requests (NE-TC-1076)', { tags: ["@Failed", "@Production"]}, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Diamond Requests', '', '', 'admin/diamond-request');
  });
});
