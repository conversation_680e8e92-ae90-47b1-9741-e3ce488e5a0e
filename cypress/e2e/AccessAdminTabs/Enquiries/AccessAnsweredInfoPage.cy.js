import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessAnsweredInfoPage', () => {
  it('Verify User Can Access Answered Info Page (NE-TC-1080)', { tags: "@Production"}, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Enquiries', 'Answered Info', '', 'admin/enquiries/answered');
  });
});
