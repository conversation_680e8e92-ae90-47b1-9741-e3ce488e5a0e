import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessToReturnInLondon', () => {
  it('Verify User Can Access To Return In London (NE-TC-1099)', { tags: ["@Failed", "@Production"]}, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments('London', 'GB', 'to-return', 'office-orders/GB/to-return');
  });
});
