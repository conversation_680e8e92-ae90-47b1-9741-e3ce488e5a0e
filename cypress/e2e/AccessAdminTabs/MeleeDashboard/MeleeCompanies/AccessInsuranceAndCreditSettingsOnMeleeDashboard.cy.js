import Login from '../../../../support/Login';
import AccessAdminTabs from '../../../../support/Admin/AccessAdminTabs';

describe('AccessInsuranceAndCreditSettingsOnMeleeDashboard', () => {
  it('Verify User Can Access Credit Request On Melee Dashboard (NE-TC-1184)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs(
      'Melee Dashboard',
      'Credit Checks',
      'Credit Request',
      'admin/credit-checks/credit_requests'
    );
  });

  it('Verify User Can Access Insurance On Melee Dashboard (NE-TC-3454)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs(
      'Melee Dashboard',
      'Credit Checks',
      'Insurance',
      'admin/companies/insurance_credit_settings'
    );
  });
});
