import Login from '../../../support/Login';
import AccessAdminTabs from '../../../support/Admin/AccessAdminTabs';

describe('AccessInsurance&CreditSettings', () => {
  it('Verify User Can Access Credit Request in Credit Checks (NE-TC-1073)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Credit Checks', 'Credit Request', '', 'admin/credit-checks/credit_requests');
  });

  it('Verify User Can Access Insurance in Credit Checks (NE-TC-3455)', { tags: '@Production' }, () => {
    const login = new Login();
    const accessAdminTabs = new AccessAdminTabs();

    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Credit Checks', 'Insurance', '', 'admin/companies/insurance_credit_settings');
  });
});
