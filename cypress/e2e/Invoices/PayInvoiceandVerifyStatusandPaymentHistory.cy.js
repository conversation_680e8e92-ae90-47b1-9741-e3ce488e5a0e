import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Admin from '../../support/Admin';
import FinanceDashboard from '../../support/FinanceDashboard';
import { skipIfPreviousTestsFailed } from 'cypress-skip-this-test';

describe('PayInvoiceandVerifyStatusandPaymentHistory', { tags: ["@Invoices", "@Regression"] }, () => {
  const login = new Login();
  const menu = new Menu();
  const admin = new Admin();
  const financeDashboard = new FinanceDashboard();
  beforeEach(skipIfPreviousTestsFailed);

  it('Send Invoice and Verify Invoices Status and Payment History (NE-TC-688)', () => {
    cy.getPayInvoice1();
    login.loginUsingAdminApi('loginasadmin.json');
    admin.accessAccounting('To Invoice');
    admin.accessInvoicesToSend();
    admin.sendInvoice('cypress/fixtures/PayInvoice1.json');
  });

  it('Pay Invoice and Verify Invoices Status and Payment History (NE-TC-688)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    admin.accessAccounting('To Invoice');
    admin.accessAwaitingPaymentTabAccounting();
    admin.searchInvoiceInAwaitingPaymentTab('cypress/fixtures/Invoice.json');
    admin.payInvoice('cypress/fixtures/Invoice.json');
  });

  it('Pay Invoice and Verify Invoices Status and Payment History (NE-TC-688)', () => {
    login.loginUsingApi('PayInvoice1.json');
    menu.visitFinancesPage();
    financeDashboard.visitPaidTab();
    financeDashboard.searchInvoice('Invoice.json');
    financeDashboard.invoiceStatusAssertion('Paid');
  });

  it('Pay Invoice and Verify Invoices Status and Payment History (NE-TC-688)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    admin.searchInvoiceAndVerifyStatus('cypress/fixtures/Invoice.json');
  });
});
