import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';
import Checkout from '../../support/Checkout';
import Admin from '../../support/Admin';
import { skipIfPreviousTestsFailed } from 'cypress-skip-this-test';

describe('CreateInvoiceandverifydetails', { tags: ["@Smoke", "@Invoices"] }, () => {
  const login = new Login();
  const product = new Product();
  const navbar = new Navbar();
  const checkout = new Checkout();
  const admin = new Admin();
  beforeEach(skipIfPreviousTestsFailed);

  it('Create Invoice and verify details (NE-TC-682)', () => {
    cy.getcreateinvoice1();
    login.loginUsingApi('credituser.json');
    navbar.visitGemStones();
    product.addProductToCart('cypress/fixtures/createinvoice1.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
  });

  it('Create Invoice and verify details (NE-TC-682)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    admin.confirmRTC(
      'Gemstone',
      'createinvoice1.json',
      'cypress/fixtures/orderNumber.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
    admin.accessRtcTab();
    admin.confirmRTC(
      'Gemstone',
      'createinvoice1.json',
      'cypress/fixtures/orderNumber.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    admin.accessAccounting('To Invoice');
    admin.createInvoice('cypress/fixtures/credituser.json');
  });
});
