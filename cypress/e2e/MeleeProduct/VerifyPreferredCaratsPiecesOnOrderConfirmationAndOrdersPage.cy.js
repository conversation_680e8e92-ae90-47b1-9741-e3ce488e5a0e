import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import Menu from '../../support/Menu';
import Orders from '../../support/Orders';

describe('VerifyPreferredCaratsPiecesOnOrderConfirmationAndOrdersPage', { tags: ['@Smoke', '@Melee-Product'] }, () => {
  it('Verify Carats/ Pieces preferred by customer is displayed on Order Confirmation page (NE-TC-2209)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const product = new Product();
    const checkout = new Checkout();

    login.loginUsingApi('SearchMelee.json');
    navbar.visitNaturalMelee();
    meleeProduct.addMeleeInputs('2.0');
    meleeProduct.addMeleeToCart();
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
    checkout.verifyCaratsAnsPiecesQuantity('2 carat');
  });

  it('Verify Carats/ Pieces preferred by customer is displayed on Orders page (NE-TC-2210)', () => {
    const login = new Login();
    const menu = new Menu();
    const orders = new Orders();

    login.loginUsingApi('SearchMelee.json');
    menu.visitOrdersPage();
    orders.VerifyAllOrdersMeleePage();
    orders.searchOrderNumber('orderNumber.json');
    orders.verifyMeleeOrderQuantity('Carats: 2ct (Required)', 'Pieces: ~');
  });
});
