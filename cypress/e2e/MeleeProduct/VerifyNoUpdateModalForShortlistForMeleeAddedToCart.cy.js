import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';

describe('VerifyNoUpdateModalForShortlistForMeleeAddedToCart', { tags: ['@Melee-Product', '@Regression'] }, () => {
  it('Verify Update Melee modal is not displayed on clicking on Shortlist icon for the melee already added to cart (NE-TC-2190)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();

    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalMelee();
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    meleeProduct.verifyUpdateModalNotVisibleForShortlist();
  });
});
