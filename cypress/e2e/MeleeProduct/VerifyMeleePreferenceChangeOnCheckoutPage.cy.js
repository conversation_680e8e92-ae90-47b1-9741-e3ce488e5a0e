import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import MeleeProduct from '../../support/MeleeProduct';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';

describe('VerifyMeleePreferenceChangeOnCheckoutPage', { tags: ["@Melee-Product", "@Regression"] }, () => {
  it('Verify melee preference can be changed on checkout page (NE-TC-2212)', () => {
    const login = new Login();
    const navbar = new Navbar();
    const meleeProduct = new MeleeProduct();
    const product = new Product();
    const checkout = new Checkout();

    login.loginUsingUniqueUsersApi();
    navbar.visitNaturalMelee();
    // meleeProduct.searchMeleeStone('addtocartmeleedata.json');
    meleeProduct.addMeleeInputs('2');
    meleeProduct.addMeleeToCart();
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.verifyCaratsAnsPiecesQuantity('2 carat');
    meleeProduct.editMeleePreference('15');
    checkout.verifyCaratsAnsPiecesQuantity('15 pieces');
  });
});
