import Login from '../../../support/Login';
import Menu from '../../../support/Menu';
import Invoices from '../../../support/Invoices';

describe('AccessPaidInvoicesTabOnInvoicesPageForSupplier', () => {
  it('Verify Supplier Can Access Paid Invoice Tab On Invoices Page (NE-TC-1249) ', { tags: ["@Failed", "@Production"]}, () => {
    const login = new Login();
    const menu = new Menu();
    const invoices = new Invoices();

    login.visitPage();
    login.loginUsingUi('loginassupplier.json');
    login.loginAsSupplierAssertion();
    menu.accessSupplierTabs('Invoices', 'invoices/all-invoices');
    invoices.accessStatusTab('Paid', 'invoices/paid');
  });
});
