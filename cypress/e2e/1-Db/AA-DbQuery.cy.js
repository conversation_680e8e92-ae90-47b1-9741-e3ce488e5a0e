describe(
  'WriteJsonFileFromDatabase',
  {
    retries: {
      runMode: 2
    },
    tags: '@DBALL'
  },
  () => {
    it('Update Password', { tags: '@DB' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `UPDATE "Users"
        SET "password" = '$2b$10$0f0QxC7lTS/ASi9.erKZfuCQ9be4fBCJe13VAIB.FTWISDA2rI3lC';`
      });
    });
    it('Delete Cart Items', { tags: '@DB' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `Delete
        from "CartItems";`
      });
    });

    it('Delete ShortList Items', { tags: '@DB' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `DELETE
        FROM "ShortlistItems";`
      });
    });
    it('Update Token', { tags: '@DB' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
        UPDATE "Users"
        SET hs_token         = NULL,
            hs_token_expires = NULL;`
      });
    });
    it('Update location', { tags: '@DB' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `UPDATE "Users"
        SET
          address_verified = true,
          "LocationId" = '729b2fda-b46d-45f3-ad91-1124d6465064',
          geo_country = 'IN',
          subtype = 'PLATFORM_AND_CFM'
        WHERE
          email = '<EMAIL>';`
      });
    });
    it('Update location', { tags: '@DB' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `UPDATE  "CompanyKycs"
        SET kyc_verified = true
        where "CompanyId"='df15102b-1549-4517-b032-fb925cc92b72';`
      });
    });

    it(
      'Get MOR Customer',
      {
        tags: ['@DBProduct', '@DBRemainingCases', '@DBOrders', '@DBProduction']
      },
      () => {
        cy.task('READFROMDB', {
          dbConfig: Cypress.config('DB'),
          sql: `SELECT u."email" AS "email", cs."CompanyId" , ("firstName" || ' ' || "lastName") AS name , CF."ACCOUNT_LIMIT" 
        FROM "Users" u  
INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
INNER JOIN "CompanyFinanceSettings" cf ON u."CompanyId" = cf."CompanyId"

WHERE 
  u."role" = 'CUSTOMER'
  AND u."default_currency" = 'USD'
  AND cs."cert_details_enabled" = true
  AND cs."accept_holds" = true
  AND u."verifyStatus" = '4'
  AND u."status" = '4'
  AND u."address_verified" = 'true'
  and cf."ACCOUNT_LIMIT" > cf."ACCOUNT_LIMIT_CONSUMED"
  AND cs.memo_enabled is null
  AND (
    u.subtype = 'PLATFORM_AND_CFM' OR u.subtype IS NULL
  )
  AND cs."display_supplier_name" = true
ORDER BY u."email"
        LIMIT 1;`
        }).then((result) => {
          cy.writeFile('cypress/fixtures/morcustomer.json', result.rows);
        });
      }
    );

    it('Get Non-MOR Customer', { tags: '@DBRemainingCases' }, () => {
      Cypress.config('defaultCommandTimeout', 600000);
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
        WHERE u."role" = 'CUSTOMER'
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = false
          and cs."accept_holds" = true
          and u."verifyStatus" = '4'
          and u."address_verified" = 'true'
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/nonmorcustomer.json', result1.rows);
      });
    });
    it('Get Cert Number', { tags: '@DBRemainingCases' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT "NaturalCertificates"."certNumber"  
      FROM "Diamonds"
      INNER JOIN "NaturalCertificates" ON "Diamonds"."CertificateId" = "NaturalCertificates"."id"
      WHERE "Diamonds"."OrderItemId" IS NULL 
      AND "Diamonds"."CertificateType" = 'NaturalCertificate' 
      AND "Diamonds"."mine_of_origin" IS NOT NULL
      AND  "Diamonds"."availability" = 'AVAILABLE'
      AND "Diamonds"."CompanyId" Not IN (
          SELECT "SupplierId" FROM "VolumeDiscounts"
      )
      limit 1`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/cert.json', result1.rows);
      });
    });
    it('Accept Hold For Customer is False', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber" 
      FROM "Users" u
      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND cs."accept_holds" = false 
        AND u."verifyStatus" = '4'
        AND nc.verified= 'true'
      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/acceptholdsfalse.json', result1.rows);
      });
    });
    it('Accept Hold For Customer is True', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber"
      FROM "Users" u
      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND d."availability" <> 'ON_HOLD'
        AND d."HoldId" IS NULL
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND cs."accept_holds" = true
        AND u."verifyStatus" = '4'
        AND nc.verified= 'true'
      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/acceptholdstrue.json', result1.rows);
      });
    });
    it('Find Shortlist Cert', { tags: ['@DBProduct', '@DBCheckout'] }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                            FROM "CartItems") si ON d."id" = si."GemstoneId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND d.availability = 'AVAILABLE'
          AND d.brown = 0
          AND d.green = 0
          AND d.milky = 0
          AND d."eyeClean" = 100
          AND d."valueWithDiscount" < 100
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND cs."accept_holds" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND nc.verified = 'true'
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/diamondshortlistcerts.json', result1.rows);
      });
    });
    it('Find Shortlist Cert 1', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                            FROM "ShortlistItems") si ON d."id" = si."DiamondId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND d.availability = 'AVAILABLE'
          AND d.brown = 0
          AND d.green = 0
          AND d.milky = 0
          AND d."eyeClean" = 100
          AND d."valueWithDiscount" < 100
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND cs."accept_holds" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND nc.verified = 'true'
        ORDER BY d."supplierStockId"
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/diamondshortlistcerts1.json', result1.rows);
      });
    });
    it('Find Gemstone Cert', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT distinct (nc."certNumber")
      FROM "Users" u
               INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
               INNER JOIN "Companies" c ON c.id=cs."CompanyId"
               INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
               INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
               LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                          FROM "CartItems") si ON d."id" = si."GemstoneId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND u."default_currency" = 'USD'
        AND c.supplier_status='LIVE'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND d.availability = 'AVAILABLE'
        AND d."dollarValue" <500
      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/gemstonecert.json', result1.rows);
      });
    });
    it('Find Gemstone stockid', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT distinct (d."NivodaStockId"  ) as certNumber
      FROM "Users" u
               INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
               INNER JOIN "Companies" c ON c.id=cs."CompanyId"
               INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
               INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
               LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                          FROM "CartItems") si ON d."id" = si."GemstoneId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND u."default_currency" = 'USD'
        AND c.supplier_status='LIVE'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND d.availability = 'AVAILABLE'
        AND d."dollarValue" <500
      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/gemstonestockid.json', result1.rows);
      });
    });
    it('Find Natural diamind Cert', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                            FROM "CartItems") si ON d."id" = si."DiamondId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND d."CertificateType" = 'NaturalCertificate'
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d."availability" = 'AVAILABLE'
          AND d.brown = 0
          AND d.green = 0
          AND d.milky = 0
          AND d."eyeClean" = 100
          AND d."valueWithDiscount" < 100
          AND nc.verified = 'true'
          AND cs.accept_returns = true
          AND qc."CertificateId" IS NULL
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/naturaldiamondcert.json', result1.rows);
      });
    });
    it('Find Lab diamind Cert', { tags: '@DBProducts' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `WITH filtered_data AS (SELECT lc."certNumber"
                       FROM "Users" u
                                INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                                INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                                INNER JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
                                LEFT JOIN (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "labId"
                                           FROM "CartItems") si ON d."id" = si."labId"
                       WHERE u."role" = 'SUPPLIER'
                         AND d."OrderItemId" IS NULL
                         AND d."CertificateType" = 'LabgrownCertificate'
                         AND u."default_currency" = 'USD'
                         AND cs."cert_details_enabled" = true
                         AND u."verifyStatus" = '4'
                         AND si."labId" IS NULL
                         AND lc.verified = 'true'
                         AND d.availability = 'AVAILABLE'
                       LIMIT 100
                        )
                        SELECT "certNumber"
                        FROM filtered_data
                        ORDER BY RANDOM()
                        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/labdiamondcert.json', result1.rows);
      });
    });
    it('Get Login As Supplier User', { tags: ['@DBProduction', '@DBRemainingCases'] }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email" , c.name
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      inner join "Companies" c on c.id = u."CompanyId" 
              WHERE u."role" = 'SUPPLIER'
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND cs."accept_holds" = true
                AND u."verifyStatus" = '4'
                AND u."status" = '4'
                AND u."address_verified" = 'true'
                and u."OwnerOfCompanyId" is not null
                AND u."LocationId" IS NOT NULL
                AND u.geo_country is not null
                AND cs."display_supplier_name" = true
                and c.api_type <> 'API'
                and c.supplier_status  = 'LIVE'
                and u.email is not null

              ORDER BY u.email
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/loginassupplier.json', result1.rows);
      });
    });
    it('Get Login As Supplier Daimonds Direct Upload User', { tags: ['@DBProduction', '@DBRemainingCases'] }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email" , c.name
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      inner join "Companies" c on c.id = u."CompanyId" 
              WHERE u."role" = 'SUPPLIER'
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND cs."accept_holds" = true
                AND u."verifyStatus" = '4'
                AND u."status" = '4'
                AND u."address_verified" = 'true'
                AND u."LocationId" IS NOT NULL
                AND u.geo_country is not null
                AND cs."display_supplier_name" = true
                AND c.supplier_status  = 'LIVE'
                AND cs.supplier_central_dashboard = FALSE
                AND c.api_type='DIRECT'
                AND c.type_detail= 'Natural Diamond Manufacturer' or c.type_detail= 'Natural Diamond Trader'
                AND cs.upload_allowed = 'true'
              ORDER BY u.email
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/loginasdirectuploadsupplier.json', result1.rows);
      });
    });
    it('Get Login As Supplier Gemstones Direct Upload User', { tags: ['@DBProduction', '@DBRemainingCases'] }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email" , c.name
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      inner join "Companies" c on c.id = u."CompanyId" 
              WHERE u."role" = 'SUPPLIER'
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND cs."accept_holds" = true
                AND u."verifyStatus" = '4'
                AND u."status" = '4'
                AND u."address_verified" = 'true'
                AND u."LocationId" IS NOT NULL
                AND u.geo_country is not null
                AND cs."display_supplier_name" = true
                AND c.supplier_status  = 'LIVE'
                AND cs.supplier_central_dashboard = FALSE
                AND c.api_type='DIRECT'
                AND c.type_detail= 'Gemstone Trader' or c.type_detail= 'Gemstone Manufacturer'
                AND cs.upload_allowed = 'true'
              ORDER BY u.email
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/loginasdirectuploadsuppliergemstone.json', result1.rows);
      });
    });
    it(
      'Get Login As Dashboard and Analytics enabledSupplier User',
      { tags: ['@DBProduction', '@DBRemainingCases'] },
      () => {
        cy.task('READFROMDB', {
          dbConfig: Cypress.config('DB'),
          sql: `SELECT u."email" AS "email" , c.name
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      inner join "Companies" c on c.id = u."CompanyId" 
              WHERE u."role" = 'SUPPLIER'
                AND u."verifyStatus" = '4'
                AND u."status" = '4'
                AND cs."display_supplier_name" = true
                and c.supplier_status  = 'LIVE'
                and cs.supplier_central_dashboard = true
                and cs.market_insights_enabled IS NOT NULL
                and cs.price_insights_enabled IS NOT NULL
              ORDER BY u.email
              LIMIT 1;`
        }).then((result1) => {
          cy.writeFile('cypress/fixtures/loginassupplierdashboardenabled.json', result1.rows);
        });
      }
    );
    it(
      'Get Login As Dashboard and Analytics Disabled Supplier ',
      { tags: ['@DBProduction', '@DBRemainingCases'] },
      () => {
        cy.task('READFROMDB', {
          dbConfig: Cypress.config('DB'),
          sql: `SELECT u."email" AS "email" , c.name
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      inner join "Companies" c on c.id = u."CompanyId" 
              WHERE u."role" = 'SUPPLIER'
                AND u."verifyStatus" = '4'
                AND u."status" = '4'
                AND cs."display_supplier_name" = true
                and c.supplier_status  = 'LIVE'
                and cs.supplier_central_dashboard = FALSE
                and cs.market_insights_enabled IS NULL
                and cs.price_insights_enabled IS NULL
              ORDER BY u.email
              LIMIT 1;`
        }).then((result1) => {
          cy.writeFile('cypress/fixtures/loginassupplierdashboardAnalyticsDisabled.json', result1.rows);
        });
      }
    );
    it('Get Login As admin User', { tags: ['@DBCheckout', '@DBProduction', '@DBRemainingCases'] }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `Select email
              from "Users" u
              where "role" = 'ADMIN'
                and u.default_currency = 'USD'
                AND u."verifyStatus" = '4'
                and u.address_verified = true
                AND u."status" = '4'
                AND u."LocationId" IS NOT NULL
                AND u.geo_country is not null
                AND u.subtype = 'PLATFORM_AND_CFM'
                AND disable_direct_login = false
              ORDER BY email desc
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/loginasadmin.json', result1.rows);
      });
    });
    it('Get Diamond Stock ID', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber", d."NivodaStockId"
      FROM "Users" u
               INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
               INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
               INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
               LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                          FROM "ShortlistItems") si ON d."id" = si."DiamondId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND d."CertificateType" = 'NaturalCertificate'
        AND d."HoldId" IS NULL
        AND d.availability='AVAILABLE'
        AND d.brown = 0
        AND d.green = 0
        AND d.milky = 0
        AND d."eyeClean" = 100
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND nc.verified= 'true'
        ORDER BY nc."certNumber"
      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/diamondstockid.json', result1.rows);
      });
    });
    it('Get Labgrown Diamond Stock ID', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT d."NivodaStockId" as certNumber
      FROM "Users" u
               INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
               INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
               INNER JOIN "LabgrownCertificates" nc ON d."CertificateId" = nc."id"
               LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                          FROM "ShortlistItems") si ON d."id" = si."DiamondId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND d."CertificateType" = 'LabgrownCertificate'
        AND d."HoldId" IS NULL
        AND d.availability='AVAILABLE'
        AND d.brown = 0
        AND d.green = 0
        AND d.milky = 0
        AND d."eyeClean" = 100
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND nc.verified= 'true'
        ORDER BY nc."certNumber"
      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/labgrowndiamondstockid.json', result1.rows);
      });
    });
    it('Get Removed Diamond Stock ID', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber", d."NivodaStockId"
      FROM "Users" u
               INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
               INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
               INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
               LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                          FROM "ShortlistItems") si ON d."id" = si."DiamondId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND d."CertificateType" = 'NaturalCertificate'
        AND d.availability='AVAILABLE'
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND nc.verified= 'true'
        AND d.brown=1
        ORDER BY nc."certNumber"
      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/removediamondstockid.json', result1.rows);
      });
    });
    it.skip('Get melee shortlist cert', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT "NivodaStockId"
        FROM (SELECT DISTINCT mc."supplierStockId" AS "NivodaStockId", cs."createdAt"
              FROM "Users" u
                       INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                       INNER JOIN "MeleeCollections" mc ON cs."CompanyId" = mc."CompanyId"
                       LEFT JOIN (SELECT "OfferId",
                                         substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "MeleeId"
                                  FROM "ShortlistItems") si ON mc."id" = si."MeleeId"
              WHERE mc."OrderItemId" IS NULL
                AND mc."IsParentMeleeCollection" IS NULL
                AND mc."ParentMeleeCollectionId" IS NULL
                AND mc."type" = 'NATURAL'
                AND mc."pieces" IS NULL
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."OfferId" IS NULL
                AND mc."source" IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
                AND mc."status" = 'LIVE'
                AND mc."OrderItemId" IS NULL
              ORDER BY cs."createdAt" DESC) subquery
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/meleeshortlistcerts.json', result1.rows);
      });
    });
    it('Search Gemstone Cert', { tags: '@DBOrders' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email" , MIN(oi."order_number") AS "ordernumber" 
      FROM "Users" u
      INNER JOIN "Orders" o ON u."id" = o."UserId"
      INNER JOIN "OrderItems" oi ON o."id" = oi."OrderId" 
      WHERE u."role" = 'CUSTOMER' 
      AND u."default_currency" = 'USD'  
      AND u."verifyStatus" = '4' 
      AND u."address_verified" = 'true'
      AND oi."ProductType" = 'Gemstone' 
      AND oi.status in ('AWAITING_PAYMENT', 'CANCELLED', 'CONFIRMED', 'IN_TRANSIT', 'PURCHASE_ORDER')
      GROUP BY u."email", u."role", u.id, u.default_currency
      HAVING COUNT(u."email") = 1
      ORDER BY MIN(oi.order_number) ASC
      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/SearchGemstone.json', result1.rows);
      });
    });
    it('Search Melee Cert', { tags: '@DBOrders' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email", MIN(oi."order_number") AS "ordernumber"
              FROM "Users" u
                      INNER JOIN "Orders" o ON u."id" = o."UserId"
                      INNER JOIN "OrderItems" oi ON o."id" = oi."OrderId"
              WHERE u."role" = 'CUSTOMER'
                AND u."default_currency" = 'USD'
                AND u."verifyStatus" = '4'
                AND u."address_verified" = 'true'
                AND oi."ProductType" = 'Melee'
                AND oi.status in ('AWAITING_PAYMENT', 'CANCELLED', 'CONFIRMED', 'IN_TRANSIT', 'PURCHASE_ORDER')
              GROUP BY u."email", u."role", u.id, u.default_currency
              LIMIT 1;`
      }).then((result1) => {
        const modifiedResult = result1.rows.map((row) => ({
          ...row,
          ordernumber: row.ordernumber.replace(/-1$/, '')
        }));
        cy.writeFile('cypress/fixtures/SearchMelee.json', modifiedResult);
      });
    });
    it('Search Diamond Cert', { tags: '@DBOrders' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email", MIN(oi."order_number") AS "ordernumber"
        FROM "Users" u
                 INNER JOIN "Orders" o ON u."id" = o."UserId"
                 INNER JOIN "OrderItems" oi ON o."id" = oi."OrderId"
        WHERE u."role" = 'CUSTOMER'
          AND u."default_currency" = 'USD'
          AND u."verifyStatus" = '4'
          AND u."address_verified" = 'true'
          AND oi."ProductType" = 'Diamond'
          AND oi.status in ('AWAITING_PAYMENT', 'CANCELLED', 'CONFIRMED', 'IN_TRANSIT', 'PURCHASE_ORDER')
        GROUP BY u."email", u."role", u.id, u.default_currency
        HAVING COUNT(u."email") = 1
        ORDER BY MIN(oi.order_number) ASC
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/Searchdiamond.json', result1.rows);
      });
    });
    it('Search Customer User', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email"
      FROM "Users" u
               INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      WHERE u."role" = 'CUSTOMER'
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        and cs."accept_holds" = true
        and u."verifyStatus" = '4'
        and u."address_verified" = 'true'
        and cs."display_supplier_name" = true
        ORDER BY u.email DESC
      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/customer.json', result1.rows);
      });
    });
    it('Search Hold Diamonds', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `WITH shortlisted_diamonds
         AS (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "DiamondId"
             FROM "ShortlistItems"),
            filtered_diamonds AS (SELECT d."CertificateId", d."NivodaStockId", nc."certNumber"
                                  FROM "Diamonds" d
                                            JOIN "NaturalCertificates" nc ON d."CertificateId" = nc.id
                                            JOIN "CompanySettings" cs ON d."CompanyId" = cs."CompanyId"
                                            JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                                  WHERE u."role" = 'SUPPLIER'
                                    AND u."default_currency" = 'USD'
                                    AND u."verifyStatus" = '4'
                                    AND cs."accept_holds" = 'true'
                                    AND cs."cert_details_enabled" = 'true'
                                    AND d."OrderItemId" IS NULL
                                    AND d."HoldId" IS NULL
                                    AND d."CertificateType" = 'NaturalCertificate'
                                    AND d."availability" = 'AVAILABLE'
                                    AND d."brown" = 0
                                    AND d."green" = 0
                                    AND d."milky" = 0
                                    AND d."eyeClean" = 100
                                    AND nc."ImageId" IS NOT NULL
                                    AND nc."VideoId" IS NOT NULL
                                    AND nc."verified" = 'true'
                                    AND d."id" NOT IN (SELECT "DiamondId" FROM shortlisted_diamonds))
        SELECT "certNumber", "NivodaStockId"
        FROM filtered_diamonds
        ORDER BY random()
        OFFSET 200 LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/diamondonhold.json', result1.rows);
      });
    });
    it('Natural Diamond Hold Request', { tags: '@DBRemainingCases' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber", d."CertificateId",cs.self_serve_holds
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "OrderRequests" o ON nc."certNumber" = o."certNumber"
              WHERE u."role" = 'SUPPLIER'
                AND u."verifyStatus" = '4'
                AND cs."cert_details_enabled" = true
                AND cs."accept_holds" = true
                AND o."certNumber" IS NULl
              AND cs.self_serve_holds IS NULL
              LIMIT 1;`
      }).then((result) => {
        const data = result.rows[0];
        if (data) {
          cy.writeFile('cypress/fixtures/naturaldiamondholdrequest.json', [data]);
          cy.task('READFROMDB', {
            dbConfig: Cypress.config('DB'),
            sql: `DELETE FROM "Diamonds"
            WHERE "CertificateId" = '${data.CertificateId}';`
          });
        }
      });
    });
    it('Natural Diamond Info Request', { tags: '@DBRemainingCases' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT DISTINCT nc."certNumber", d."CertificateId"
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "OrderRequests" o ON nc."certNumber" = o."certNumber"
              WHERE u."role" = 'SUPPLIER'
                AND u."verifyStatus" = '4'
                AND cs."cert_details_enabled" = true
                AND o."certNumber" IS NULL
              ORDER BY d."CertificateId"
              LIMIT 1;`
      }).then((result) => {
        const data = result.rows[0];
        if (data) {
          cy.writeFile('cypress/fixtures/naturaldiamondinforequest.json', [data]);
          cy.task('READFROMDB', {
            dbConfig: Cypress.config('DB'),
            sql: `DELETE FROM "Diamonds"
            WHERE "CertificateId" = '${data.CertificateId}';`
          });
        }
      });
    });
    it('Natural Diamond Buy Request', { tags: '@DBRemainingCases' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
      SELECT nc."certNumber", d."CertificateId"
      FROM "Diamonds" d
      JOIN "NaturalCertificates" nc ON d."CertificateId" = nc.id
      JOIN "CompanySettings" cs ON d."CompanyId" = cs."CompanyId"
      LEFT JOIN "OrderRequests" o ON o."certNumber" = nc."certNumber"
      WHERE cs."cert_details_enabled" = TRUE
        AND o."certNumber" IS NULL
        AND d."CertificateId" IS NOT NULL
        AND d."CompanyId" IN (
          SELECT DISTINCT "CompanyId"
          FROM "Users"
          WHERE "role" = 'SUPPLIER'
            AND "verifyStatus" = '4'
        )
      LIMIT 1;
    `
      }).then((result) => {
        const data = result.rows[0];
        if (data) {
          cy.writeFile('cypress/fixtures/naturaldiamondbuyrequest.json', [data]);
          cy.task('READFROMDB', {
            dbConfig: Cypress.config('DB'),
            sql: `DELETE FROM "Diamonds" WHERE "CertificateId" = '${data.CertificateId}';`
          });
        }
      });
    });

    it('Labgrown Diamond Request', { tags: '@DBRemainingCases' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber" , d."CertificateId"
              FROM "Users" u
              INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
              INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
              INNER JOIN "LabgrownCertificates" nc ON d."CertificateId" = nc."id"
              WHERE u."role" = 'SUPPLIER'
                AND d."OrderItemId" IS NOT NULL
                AND d.availability = 'NOT_AVAILABLE'
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND cs."accept_holds" = true
                AND u."verifyStatus" = '4'
                AND nc.verified= 'true'
                AND NOT EXISTS (
                  SELECT 1
                  FROM "OrderRequests" o
                  WHERE nc."certNumber" = o."certNumber"
                )
              LIMIT 1;`
      }).then((result) => {
        const data = result.rows[0];
        if (data) {
          cy.writeFile('cypress/fixtures/labgrowndiamondrequest.json', [data]);
          cy.task('READFROMDB', {
            dbConfig: Cypress.config('DB'),
            sql: `DELETE FROM "Diamonds"
            WHERE "CertificateId" = '${data.CertificateId}';`
          });
        }
      });
    });
    it('Diamond Group Data', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber", d."NivodaStockId"
          FROM "Users" u
                   INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                   INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                   INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                   LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                              FROM "ShortlistItems") si ON d."id" = si."DiamondId"
          WHERE u."role" = 'SUPPLIER'
            AND d."OrderItemId" IS NULL
            AND d.availability='AVAILABLE'
            AND u."default_currency" = 'USD'
            AND cs."cert_details_enabled" = true
            AND cs."accept_holds" = true
            AND u."verifyStatus" = '4'
            AND nc.verified= 'true'
            AND si."OfferId" IS NULL
          ORDER BY d."NivodaStockId"
          LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/diamondgroupdata.json', result1.rows);
      });
    });
    it.skip('Melee Group Data', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT DISTINCT d."supplierStockId" AS "NivodaStockId", d."createdAt"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "MeleeCollections" d ON cs."CompanyId" = d."CompanyId"
                 LEFT JOIN (SELECT "OfferId",
                                   substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "MeleeId"
                            FROM "ShortlistItems") si ON d."id" = si."MeleeId"
        WHERE d."OrderItemId" IS NULL
          AND d."IsParentMeleeCollection" IS NULL
          AND d."ParentMeleeCollectionId" IS NULL
          AND d."type" = 'NATURAL'
          AND d.pieces IS NULL
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.status = 'LIVE'
          AND d.source IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
          AND d."createdAt" > (NOW() - INTERVAL '500 DAY')
        ORDER BY d."createdAt" DESC, d."supplierStockId" DESC
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/meleegroupdata.json', result1.rows);
      });
    });
    it('Gems Group Data', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber",
          CONCAT(
                  INITCAP(nc."gemType"), ' ',
                  LOWER(nc.color), ' ',
                  LOWER(nc.shape), ' ',
                  nc.carats
          ) AS "description"
            FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Companies" c ON c.id=cs."CompanyId"
                      INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemId"
                                FROM "ShortlistItems") si ON d."id" = si."GemId"
            WHERE u."role" = 'SUPPLIER'
              AND d."OrderItemId" IS NULL
              AND d."HoldId" IS NULL
              AND u."default_currency" = 'USD'
              AND cs."cert_details_enabled" = true
              AND u."verifyStatus" = '4'
              AND c.supplier_status='LIVE'
              AND si."OfferId" IS NULL
              AND d.availability = 'AVAILABLE'
              order by nc."certNumber"
            LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/gemsgroupdata.json', result1.rows);
      });
    });
    it('Gems Front Data', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                 INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemId"
                            FROM "ShortlistItems") si ON d."id" = si."GemId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND d."HoldId" IS NULL
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND c.supplier_status = 'LIVE'
          AND si."OfferId" IS NULL
          AND d.availability = 'AVAILABLE'
        order by d."CompanyId" desc
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/gemsupfrontdata.json', result1.rows);
      });
    });
    it('Gems ShortList Cert', { tags: ['@DBProduct', '@DBCheckout'] }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `WITH random_selection AS (SELECT nc."certNumber"
                          FROM "Users" u
                                   INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                                   INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                                   INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                                   INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                                   LEFT JOIN (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "GemId"
                                              FROM "ShortlistItems") si ON d."id" = si."GemId"
                          WHERE u."role" = 'SUPPLIER'
                            AND d."OrderItemId" IS NULL
                            AND d."HoldId" IS NULL
                            AND u."default_currency" = 'USD'
                            AND cs."cert_details_enabled" = true
                            AND u."verifyStatus" = '4'
                            AND c.supplier_status = 'LIVE'
                            AND si."GemId" IS NULL
                            AND d.availability = 'AVAILABLE')
                            SELECT "certNumber"
                            FROM random_selection
                            ORDER BY RANDOM()
                            LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/gemsshortlistcert.json', result1.rows);
      });
    });
    it('Add To Cart Gems Data', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `WITH shortlisted_gems AS (SELECT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "GemId"
                          FROM "ShortlistItems")
              SELECT nc."certNumber",
                    CONCAT(INITCAP(nc."gemType"), ' ', LOWER(nc.color), ' ', LOWER(nc.shape), ' ', nc.carats) AS "description"
              FROM "Users" u
                      JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      JOIN "Companies" c ON c.id = cs."CompanyId" AND c.supplier_status = 'LIVE'
                      JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                      JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN shortlisted_gems si ON d."id" = si."GemId"
              WHERE u."role" = 'SUPPLIER'
                AND u."default_currency" = 'USD'
                AND u."verifyStatus" = '4'
                AND cs."cert_details_enabled" = true
                AND d."OrderItemId" IS NULL
                AND d."HoldId" IS NULL
                AND d.availability = 'AVAILABLE'
                AND si."GemId" IS NULL
              ORDER BY nc."certNumber" DESC
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/addtocartgemsdata.json', result1.rows);
      });
    });
    it.skip('Add To Cart melee Data', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT d."supplierStockId" AS "NivodaStockId"
              FROM "Users" u
                      JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      JOIN "MeleeCollections" d ON cs."CompanyId" = d."CompanyId"
                      LEFT JOIN (SELECT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "MeleeId"
                                  FROM "ShortlistItems") si ON d."id" = si."MeleeId"
              WHERE d."OrderItemId" IS NULL
                AND d."IsParentMeleeCollection" IS NULL
                AND d."ParentMeleeCollectionId" IS NULL
                AND d."type" = 'NATURAL'
                AND d.pieces IS NULL
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."MeleeId" IS NULL
                AND d.status = 'LIVE'
                AND source IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
              ORDER BY RANDOM()
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/addtocartmeleedata.json', result1.rows);
      });
    });
    it('Remove Gems Data', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT MAX(nc."certNumber") AS "certNumber",
        CONCAT(
                INITCAP(nc."gemType"), ' ',
                LOWER(nc.color), ' ',
                LOWER(nc.shape), ' ',
                nc.carats
        )                    AS "description"
 FROM "Users" u
          INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
          INNER JOIN "Companies" c ON c.id=cs."CompanyId"
          INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
          INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
          LEFT JOIN (SELECT "OfferId",
                            substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemId"
                     FROM "ShortlistItems") si ON d."id" = si."GemId"
 WHERE u."role" = 'SUPPLIER'
   AND d."OrderItemId" IS NULL
   AND d."HoldId" IS NULL
   AND u."default_currency" = 'USD'
   AND cs."cert_details_enabled" = true
   AND u."verifyStatus" = '4'
   AND c.supplier_status='LIVE'
   AND si."OfferId" IS NULL
   AND d.availability = 'AVAILABLE'
 GROUP BY INITCAP(nc."gemType"),
          LOWER(nc.color),
          LOWER(nc.shape),
          nc.carats
 LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/removegemsdata.json', result1.rows);
      });
    });
    it('Remove Melee Data', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT (d."supplierStockId") AS "NivodaStockId"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "MeleeCollections" d ON cs."CompanyId" = d."CompanyId"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "MeleeId"
                            FROM "ShortlistItems") si ON d."id" = si."MeleeId"
        WHERE d."OrderItemId" IS NULL
          AND d."IsParentMeleeCollection" is NULL
          AND d."ParentMeleeCollectionId" is NULL
          AND d."type" = 'NATURAL'
          AND d.pieces IS NULL
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.status = 'LIVE'
          AND source in ('CONSIGNMENT', 'SOURCE_TO_ORDER')
        ORDER BY cs."CompanyId"
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/removemeleedata.json', result1.rows);
      });
    });
    it('Get Company Owner User', { tags: ['@DBRemainingCases'] }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
        SELECT u."email"
        FROM "Users" u
        WHERE u."role" = 'CUSTOMER'
          AND u."default_currency" = 'USD'
          AND u."verifyStatus" = '4'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."OwnerOfCompanyId" IS NOT NULL
          AND u."LocationId" IS NOT NULL
          AND u.geo_country is not null
          AND (
            SELECT COUNT(*) 
            FROM "Users" u2 
            WHERE u2."email" = u."email"
          ) = 1
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/companyowner.json', result1.rows);
      });
    });
    it('Get Diamond Pair Data', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber"
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "DiamondId"
                                  FROM "CartItems") si ON d."id" = si."DiamondId"
              WHERE u."role" = 'SUPPLIER'
                AND d."OrderItemId" IS NULL
                AND d."CertificateType" = 'NaturalCertificate'
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."DiamondId" IS NULL
                AND d."availability" = 'AVAILABLE'
                AND d.brown = 0
                AND d.green = 0
                AND d.milky = 0
                AND d."eyeClean" = 100
                AND nc.verified = 'true'
                AND cs."accept_returns" = true
              ORDER BY nc."certNumber" DESC
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/diamondpairdata.json', result1.rows);
      });
    });
    it('Get Lab Diamond Pair Data', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT lc."certNumber"
      FROM "Users" u
               INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
               INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
               INNER JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
               LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "labId"
                          FROM "CartItems") si ON d."id" = si."labId"
      WHERE u."role" = 'SUPPLIER'
        AND d."OrderItemId" IS NULL
        AND d."CertificateType" = 'LabgrownCertificate'
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND u."verifyStatus" = '4'
        AND si."OfferId" IS NULL
        AND lc.verified= 'true'
        and d.availability = 'AVAILABLE'
        ORDER BY lc."certNumber" ASC
      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/labdiamondpairdata.json', result1.rows);
      });
    });
    it('Get Short List Lab Diamond Data', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT lc."certNumber", d."NivodaStockId"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "labId"
                            FROM "CartItems") si ON d."id" = si."labId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND d."CertificateType" = 'LabgrownCertificate'
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND lc.verified = 'true'
          AND d.availability = 'AVAILABLE'
          ORDER BY cs."CompanyId" DESC
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/shortlistlabdiamond.json', result1.rows);
      });
    });
    it('Get Short List Lab Diamond Data 1', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `WITH random_diamond AS (SELECT d."id"
        FROM "Diamonds" d
                 TABLESAMPLE SYSTEM (1)
        WHERE d."OrderItemId" IS NULL
          AND d.availability = 'AVAILABLE')
        SELECT lc."certNumber", d."NivodaStockId"
        FROM random_diamond rd
        JOIN "Diamonds" d ON d."id" = rd."id"
        JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
        JOIN "Users" u ON u."CompanyId" = d."CompanyId"
        JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
        LEFT JOIN (SELECT substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
            FROM "ShortlistItems") si ON d."id" = si."DiamondId"
        WHERE u."role" = 'SUPPLIER'
        AND u."default_currency" = 'USD'
        AND cs."cert_details_enabled" = true
        AND cs."accept_holds" = true
        AND u."verifyStatus" = '4'
        AND lc.verified = 'true'
        AND si."DiamondId" IS NULL
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/shortlistlabdiamond1.json', result1.rows);
      });
    });
    it('Get Short List Lab Diamond Pair', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT lc."certNumber", d."NivodaStockId"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                            FROM "ShortlistItems") si ON d."id" = si."DiamondId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND cs."accept_holds" = true
          AND u."verifyStatus" = '4'
          AND lc.verified= 'true'
          AND si."OfferId" IS NULL
        ORDER BY lc."certNumber"
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/shortlistlabdiamondpair.json', result1.rows);
      });
    });
    it('Get Non - Verified User', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email"
              FROM "Users" u
              WHERE u."role" = 'CUSTOMER'
                AND u."default_currency" = 'USD'
                and u."verifyStatus" <> 4
                and u."address_verified" = 'true'
              ORDER BY u.email
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/nonverifiedusers.json', result1.rows);
      });
    });
    it('Get Multiple Stone Pair', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `WITH filtered_data AS (SELECT d."CertificateId"
        FROM "Users" u
                 JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
                 LEFT JOIN (SELECT DISTINCT (substring("OfferId" FROM position('/' IN "OfferId") + 1))::uuid AS diamond_id
                            FROM "CartItems") si ON d."CertificateId" = si.diamond_id
        WHERE u."role" = 'SUPPLIER'
          AND u."default_currency" = 'USD'
          AND u."verifyStatus" = '4'
          AND cs."cert_details_enabled" = true
          AND d."OrderItemId" IS NULL
          AND d."CertificateType" = 'LabgrownCertificate'
          AND d."availability" = 'AVAILABLE'
          AND lc."verified" = 'true'
          AND si.diamond_id IS NULL
        LIMIT 1000 
        )
        SELECT lc."certNumber"
        FROM "LabgrownCertificates" lc
        JOIN filtered_data fd ON lc."id" = fd."CertificateId"
        ORDER BY RANDOM()
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/multiplestonepair.json', result1.rows);
      });
    });
    it('Get Invoice Data', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u.email AS email, i.invoice_number as invoice
        FROM "Users" u
                 INNER JOIN "Orders" o ON u.id = o."UserId"
                 INNER JOIN "Companies" c ON c.id = u."CompanyId"
                 INNER JOIN "OrderItems" oi ON o.id = oi."OrderId"
                 INNER JOIN "InvoiceOrderItems" ioi ON oi.id = ioi."OrderItemId"
                 INNER JOIN "Invoices" i ON ioi."InvoiceId" = i.id
        WHERE i.type = 'sell'
          AND i.status='PAID'
          AND i.invoice_sent IS NOT NULL
          AND i.invoiced_at IS NOT NULL
        GROUP BY u.email, i.invoice_number, i."createdAt", i.status, i.type
        ORDER BY i."createdAt" DESC
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/invoicedata.json', result1.rows);
      });
    });
    it('Get Non Kyc User', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email"
        FROM "Users" u
        INNER JOIN "Companies" c ON c.id = u."CompanyId"
        LEFT JOIN "CompanyKycs" ck ON ck."CompanyId" = c.id
        WHERE u."role" = 'CUSTOMER'
          AND c."createdAt" > '2023-06-16'
          AND ck."CompanyId" IS NULL
          AND u."verifyStatus"='4'
        ORDER BY c."createdAt" DESC
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/nonkycuser.json', result1.rows);
      });
    });
    it('Get Upfront Legacy User', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `select u.email
        from "CompanyFinanceSettings" cf
                 INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                 INNER JOIN "Companies" c ON c.id = u."CompanyId"
                 INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
        where cf."ADVANCE_PAYMENT" = true
          and cf."CREDIT_LIMIT" > 15000
          and cf."DISABLE_CHECKOUT" = false
          and cf."ACCOUNT_LIMIT" > 15000
          AND cf."DEFAULT_CHECKOUT_OPTION" = 'up_front_payment'
          AND cf."ALL_IN_PRICING" = false
          AND c.code_disabled is null
          AND u."verifyStatus" = 4
          AND u."role" = 'CUSTOMER'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."LocationId" IS NOT NULL
          AND u.geo_country is not null
          AND u.subtype= 'PLATFORM_AND_CFM'
          AND ck.kyc_verified = true
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/upfrontlegacyuser.json', result1.rows);
      });
    });
    it('Get Upfront Api User', { tags: '@DBCheckout' }, () => {
      Cypress.config('defaultCommandTimeout', 600000);
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `select u.email , c."name" ,u.id
        from "CompanyFinanceSettings" cf
                 INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                 INNER JOIN "Companies" c ON c.id = u."CompanyId"
                 LEFT JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
        where cf."ADVANCE_PAYMENT" = true
          and cf."DISABLE_CHECKOUT" = false
          AND cf."DEFAULT_CHECKOUT_OPTION" = 'up_front_payment'
          AND cf."ALL_IN_PRICING" = true
          AND cf."ACCOUNT_LIMIT" > 500000
          AND cf."DISABLE_CHECKOUT" is false
          AND c.code_disabled is null
          AND u."verifyStatus" = 4
          AND u."role" = 'CUSTOMER'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."LocationId" IS NOT NULL
          AND u.geo_country is not null
          AND (u.subtype = 'PLATFORM_AND_CFM' OR u.subtype IS NULL)
          AND ck.kyc_verified = true
        ORDER BY u.email DESC
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/upfrontaipuser.json', result1.rows);
      });
    });
    it('Get Incentive Pay Legacy User', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u.email,
        '$' || TO_CHAR(cf."ACCOUNT_LIMIT" - cf."ACCOUNT_LIMIT_CONSUMED", 'FM999,999,999.00') AS "Total_Account_Limit"
        FROM "CompanyFinanceSettings" cf
                  INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                  INNER JOIN "Companies" c ON c.id = u."CompanyId"
                  LEFT JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
        WHERE cf."ADVANCE_PAYMENT" = false
          AND cf."DISABLE_CHECKOUT" = false
          AND cf."DEFAULT_CHECKOUT_OPTION" = 'ip_payment'
          AND cf."ALL_IN_PRICING" = false
          AND cf."ACCOUNT_LIMIT" > 20000
          AND cf."IP_ACTIVE" = true
          AND cf."MARKET_PAY_ACTIVE"= false
          AND c.code_disabled is null
          AND u."verifyStatus" = 4
          AND u."role" = 'CUSTOMER'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."LocationId" IS NOT NULL
          AND u.geo_country IS NOT NULL
          AND u.subtype <> 'CFM' 
          AND ck.kyc_verified = false
        ORDER BY u.email DESC
        LIMIT 1`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/incentivepaylegacyuser.json', result1.rows);
      });
    });
    it('Get Short List Lab Diamond Data 1', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u.email, cf."ACCOUNT_LIMIT_CONSUMED",
        '$' || TO_CHAR(cf."ACCOUNT_LIMIT" - cf."ACCOUNT_LIMIT_CONSUMED", 'FM999,999,999.00') AS "Total_Account_Limit"
        FROM "CompanyFinanceSettings" cf
                  INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                  INNER JOIN "Companies" c ON c.id = u."CompanyId"
                  LEFT JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
        WHERE cf."ADVANCE_PAYMENT" = false
          AND cf."DISABLE_CHECKOUT" = false
          AND cf."DEFAULT_CHECKOUT_OPTION" = 'ip_payment'
          AND cf."ALL_IN_PRICING" = true
          AND cf."ACCOUNT_LIMIT" > 20000
          AND cf."IP_ACTIVE" = true
          AND cf."MARKET_PAY_ACTIVE" = false
          AND cf."MPF_CHARGED" = true
          AND c.code_disabled is null
          AND u."verifyStatus" = 4
          AND u."role" = 'CUSTOMER'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."LocationId" IS NOT NULL
          AND u.subtype = 'PLATFORM_AND_CFM'
          AND u."default_currency" = 'USD'
          AND ck.kyc_verified = false
          AND c."createdAt" < '2023-06-16'
        ORDER BY u.email
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/incentivepayaipuser.json', result1.rows);
      });
    });
    it('Get Credit User', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
            SELECT u.email,
       c."name",
       cf."ACCOUNT_LIMIT_CONSUMED",
       '$' || TO_CHAR(cf."ACCOUNT_LIMIT" - cf."ACCOUNT_LIMIT_CONSUMED", 'FM999,999,999.00') AS "Total_Account_Limit"
        FROM "CompanyFinanceSettings" cf
                INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                INNER join "Companies" c on U."CompanyId" = c.id
                INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
        WHERE cf."ADVANCE_PAYMENT" = false
          AND cf."DISABLE_CHECKOUT" = false
          AND cf."DEFAULT_CHECKOUT_OPTION" = 'cr_30_payment'
          AND cf."ALL_IN_PRICING" = true
          AND cf."ACCOUNT_LIMIT" > 20000
          AND cf."CREDIT_LIMIT" > 20000
          AND cf."IP_ACTIVE" = false
          AND u."verifyStatus" = 4
          AND u."role" = 'CUSTOMER'
          AND u."status" = '4'
          AND u."address_verified" = true
          AND u."LocationId" IS NOT NULL
          AND u."geo_country" IS NOT NULL
          AND u."subtype" = 'PLATFORM_AND_CFM'
          AND ck."kyc_verified" = true
        LIMIT 1;
        `
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/credituser.json', result1.rows);
      });
    });
    it.skip('Get melee add to cart data', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `WITH ranked_data AS (SELECT mc."supplierStockId" AS "NivodaStockId",
          ROW_NUMBER() OVER () AS rn
          FROM "Users" u
                    INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                    INNER JOIN "MeleeCollections" mc ON cs."CompanyId" = mc."CompanyId"
                    LEFT JOIN (SELECT DISTINCT (substring("OfferId" FROM position('/' IN "OfferId") + 1))::uuid AS "MeleeId"
                              FROM "CartItems") ci ON mc."id" = ci."MeleeId"
          WHERE mc."OrderItemId" IS NULL
            AND mc."IsParentMeleeCollection" IS NULL
            AND mc."ParentMeleeCollectionId" IS NULL
            AND mc."type" = 'NATURAL'
            AND mc.pieces IS NULL
            AND u."default_currency" = 'USD'
            AND cs."cert_details_enabled" = true
            AND u."verifyStatus" = '4'
            AND ci."MeleeId" IS NULL
            AND mc."source" IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
            AND mc."status" = 'LIVE'),
        random_row AS (SELECT "NivodaStockId"
          FROM ranked_data
          WHERE rn = (SELECT floor(random() * count(*) + 1) FROM ranked_data))
        SELECT "NivodaStockId"
        FROM random_row;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/meleeaddtocart.json', result1.rows);
      });
    });
    it('Get upfront pay order Data', { tags: ['@DBCheckout', '@DBRemainingCases'] }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT lc."certNumber",
                d."CertificateId",
                c."name"
          FROM "Users" u
                  INNER JOIN
              "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                  INNER JOIN
              "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                  INNER JOIN
              "Companies" c ON u."CompanyId" = c."id"
                  INNER JOIN
              "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
                  LEFT JOIN (SELECT "OfferId",
                                    substring("OfferId" FROM position('/' in "OfferId") + 1)::uuid AS "labId"
                              FROM "CartItems") si ON d."id" = si."labId"
          WHERE u."role" = 'SUPPLIER'
            AND d."OrderItemId" IS NULL
            AND d."CertificateType" = 'LabgrownCertificate'
            AND u."default_currency" = 'USD'
            AND cs."cert_details_enabled" = true
            AND cs."accept_holds" = 'true'
            AND u."verifyStatus" = '4'
            AND si."OfferId" IS NULL
            AND lc."verified" = 'true'
            AND d."availability" = 'AVAILABLE'
            AND c."name" NOT LIKE 'xxxx%'
          LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/upfrontpayorder.json', result1.rows);
      });
    });
    it('Get incentive pay order aip', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT distinct (nc."certNumber"), d."CompanyId"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                 INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                            FROM "CartItems") si ON d."id" = si."GemstoneId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND u."default_currency" = 'USD'
          AND c.supplier_status = 'LIVE'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.availability = 'AVAILABLE'
          AND d."dollarValue" < 500
        ORDER BY d."CompanyId"
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/incentivepayorderaip.json', result1.rows);
      });
    });
    it('create invoice', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT distinct (nc."certNumber"), nc.id
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                 INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                            FROM "CartItems") si ON d."id" = si."GemstoneId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND u."default_currency" = 'USD'
          AND c.supplier_status = 'LIVE'
          AND u."geo_country" <> 'TH'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.availability = 'AVAILABLE'
          AND d."dollarValue" < 500
        ORDER BY nc."certNumber"
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/createinvoice.json', result1.rows);
      });
    });
    it('Get create invoice 1', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT distinct (nc."certNumber"), nc.id , c.name
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                 INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                            FROM "CartItems") si ON d."id" = si."GemstoneId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS NULL
          AND u."default_currency" = 'USD'
          AND c.supplier_status = 'LIVE'
          AND u."geo_country" <> 'TH'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.availability = 'AVAILABLE'
          AND d."dollarValue" < 500
        ORDER BY nc."certNumber" DESC
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/createinvoice1.json', result1.rows);
      });
    });
    it.skip('Get gems data', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT distinct (nc."certNumber"), c.name
          FROM "Users" u
                   INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                   INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                   INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                   INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                   LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "GemstoneId"
                              FROM "CartItems") si ON d."id" = si."GemstoneId"
          WHERE u."role" = 'SUPPLIER'
            AND d."OrderItemId" IS NULL
            AND u."default_currency" = 'USD'
            AND c.supplier_status = 'LIVE'
            AND cs."cert_details_enabled" = true
            AND u."verifyStatus" = '4'
            AND si."OfferId" IS NULL
            AND d.availability = 'AVAILABLE'
            AND d."dollarValue" < 500
          ORDER BY c.name
          LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/gemsdata.json', result1.rows);
      });
    });
    it('Get gems data 1', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT "certNumber", name
                FROM (SELECT DISTINCT nc."certNumber", c.name, RANDOM() as rand
                      FROM "Users" u
                              INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                              INNER JOIN "Companies" c ON c.id = cs."CompanyId"
                              INNER JOIN "Gemstones" d ON cs."CompanyId" = d."CompanyId"
                              INNER JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                              LEFT JOIN (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "GemstoneId"
                                          FROM "CartItems") si ON d."id" = si."GemstoneId"
                      WHERE u."role" = 'SUPPLIER'
                        AND d."OrderItemId" IS NULL
                        AND u."default_currency" = 'USD'
                        AND c.supplier_status = 'LIVE'
                        AND cs."cert_details_enabled" = true
                        AND u."verifyStatus" = '4'
                        AND si."GemstoneId" IS NULL
                        AND d.availability = 'AVAILABLE'
                        AND d."dollarValue" < 500) AS subquery
                ORDER BY rand
                LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/gemsdata1.json', result1.rows);
      });
    });
    it('Verify Status Of Invoice', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT i."invoice_number" as Invoice, i."status", MIN(u."email") AS "email"
            FROM "Invoices" i
            INNER JOIN "Companies" cs ON i."to" = cs."id"
            INNER JOIN "Users" u ON cs."id" = u."CompanyId"
            WHERE i."status" = 'PAID'
            GROUP BY i."invoice_number", i."status"
            LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/VerifyStatusOfInvoice.json', result1.rows);
      });
    });
    it('Get Pay Invoice Data', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
            WITH CompanyDetails AS (
              SELECT u.email, c."name"
              FROM "CompanyFinanceSettings" cf
              INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
              INNER JOIN "Companies" c ON u."CompanyId" = c.id 
              INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
              WHERE cf."ADVANCE_PAYMENT" = false
                AND cf."DISABLE_CHECKOUT" = false
                AND cf."DEFAULT_CHECKOUT_OPTION" = 'cr_30_payment'
                AND cf."ALL_IN_PRICING" = true
                AND cf."ACCOUNT_LIMIT" > 20000
                AND cf."CREDIT_LIMIT" > 20000
                AND cf."IP_ACTIVE" = false
                AND u."verifyStatus" = 4
                AND u."role" = 'CUSTOMER'
                AND u."status" = '4'
                AND u."address_verified" = true
                AND u."LocationId" IS NOT NULL
                AND u."geo_country" IS NOT NULL
                AND u."subtype" = 'PLATFORM_AND_CFM'
                AND ck."kyc_verified" = true
            )
            SELECT 
              i."invoice_number" AS invoice, 
              u.email AS email,
              cd."name" AS "name"
            FROM "Invoices" i
            INNER JOIN "Companies" cs ON i."to" = cs."id"
            INNER JOIN "Users" u ON cs."id" = u."CompanyId"
            INNER JOIN CompanyDetails cd ON u.email = cd.email
            WHERE i."status" is NULL
            AND i."type" <> 'CREDIT_NOTE'
            GROUP BY i."invoice_number", u.email, cd."name" order by i.invoice_number desc
            LIMIT 1;
          `
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/PayInvoice.json', result1.rows);
      });
    });
    it('Get Pay Invoice Data 1', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
            WITH CompanyDetails AS (
              SELECT u.email, c."name"
              FROM "CompanyFinanceSettings" cf
              INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
              INNER JOIN "Companies" c ON u."CompanyId" = c.id 
              INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
              WHERE cf."ADVANCE_PAYMENT" = false
                AND cf."DISABLE_CHECKOUT" = false
                AND cf."DEFAULT_CHECKOUT_OPTION" = 'cr_30_payment'
                AND cf."ALL_IN_PRICING" = true
                AND cf."ACCOUNT_LIMIT" > 20000
                AND cf."CREDIT_LIMIT" > 20000
                AND cf."IP_ACTIVE" = false
                AND u."verifyStatus" = 4
                AND u."role" = 'CUSTOMER'
                AND u."status" = '4'
                AND u."address_verified" = true
                AND u."LocationId" IS NOT NULL
                AND u."geo_country" IS NOT NULL
                AND u."subtype" = 'PLATFORM_AND_CFM'
                AND ck."kyc_verified" = true
            )
            SELECT 
              i."invoice_number" AS invoice, 
              u.email AS email,
              cd."name" AS "name"
            FROM "Invoices" i
            INNER JOIN "Companies" cs ON i."to" = cs."id"
            INNER JOIN "Users" u ON cs."id" = u."CompanyId"
            INNER JOIN CompanyDetails cd ON u.email = cd.email
            WHERE i."status" is NULL
            And i.invoice_sent is null
            AND i."type" <> 'CREDIT_NOTE'
            GROUP BY i."invoice_number", u.email, cd."name" order by i.invoice_number ASC
            LIMIT 1;
          `
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/PayInvoice1.json', result1.rows);
      });
    });
    it('Get Pay Partial Invoice Data ', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT DISTINCT ON (i."invoice_number") 
              i."invoice_number" AS invoice, 
              u.email AS email,
              c."name" AS name,
              i."createdAt"
            FROM "Invoices" i
            INNER JOIN "Companies" cs ON i."to" = cs."id"
            INNER JOIN "Users" u ON cs."id" = u."CompanyId"
            INNER JOIN "Companies" c ON u."CompanyId" = c.id 
            INNER JOIN "CompanyFinanceSettings" cf ON u."CompanyId" = cf."CompanyId"
            INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
            WHERE 
              u."verifyStatus" = 4
              AND u."role" = 'CUSTOMER'
              AND u."status" = '4'
              AND u."address_verified" = true
              AND u."LocationId" IS NOT NULL
              AND u."geo_country" IS NOT NULL
              AND ck."kyc_verified" = true
              AND (i."status" = 'PARTIAL_PAID' OR i."status" IS NULL)
              and i."type" <> 'CREDIT_NOTE'
            ORDER BY 
              i."invoice_number"
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/PayPartialInvoice.json', result1.rows);
      });
    });
    it('Get multiple address user', { tags: ['@DBCreditNote', '@DBCheckout'] }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email"                                                                              AS "email",
                    c.name                                                                                 AS "name",
                    cfs."ACCOUNT_LIMIT_CONSUMED",
                    '$' || TO_CHAR(cfs."ACCOUNT_LIMIT" - cfs."ACCOUNT_LIMIT_CONSUMED", 'FM999,999,999.00') AS "Total_Account_Limit",
                    CONCAT_WS(', ', l1."address1", CONCAT(l1."postalCode", ' ', l1."city"), l1."country")  AS "location_1",
                    CONCAT_WS(', ', l2."address1", CONCAT(l2."postalCode", ' ', l2."city"), l2."country")  AS "location_2"
              FROM "Users" u
                      INNER JOIN
                  "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "CompanyFinanceSettings" cfs on cfs."CompanyId" = u."CompanyId"
                      INNER join "Companies" c on u."CompanyId" = c.id
                      INNER JOIN
                  (SELECT "CompanyId"
                    FROM "Locations"
                    GROUP BY "CompanyId"
                    HAVING COUNT(*) = 2) lc ON u."CompanyId" = lc."CompanyId"
                      INNER JOIN
                  "Locations" l1 ON u."CompanyId" = l1."CompanyId"
                      INNER JOIN
                  "Locations" l2 ON u."CompanyId" = l2."CompanyId"
                      AND l1."id" <> l2."id"
                      AND (l1."address1" <> l2."address1" OR l1."postalCode" <> l2."postalCode" OR l1."city" <> l2."city" OR
                            l1."country" <> l2."country")
              WHERE u."role" = 'CUSTOMER'
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND cs."accept_holds" = true
                AND u."verifyStatus" = '4'
                AND u."status" = '4'
                AND u."address_verified" = true
                AND cs."display_supplier_name" = true
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/multipleaddressuser.json', result1.rows);
      });
    });
    it('Get multiple address stone 1', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber"
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                      LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                                  FROM "CartItems") si ON d."id" = si."DiamondId"
              WHERE u."role" = 'SUPPLIER'
                AND d."OrderItemId" IS NULL
                AND d."CertificateType" = 'NaturalCertificate'
                AND u."default_currency" = 'INR'
                AND d.brown = 0
                AND d.green = 0
                AND d.milky = 0
                AND d."eyeClean" = 100
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."OfferId" IS NULL
                AND d."availability" = 'AVAILABLE'
                AND nc.verified = 'true'
                AND qc."CertificateId" IS NULL
                AND geo_country = 'IN'
                AND cs.self_serve_po IS NULL
                ORDER BY NC."certNumber"
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/multipleaddressstone1.json', result1.rows);
      });
    });
    it('Get multiple address stone 2', { tags: '@DBCheckout' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber"
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                      LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                                  FROM "CartItems") si ON d."id" = si."DiamondId"
              WHERE u."role" = 'SUPPLIER'
                AND d."OrderItemId" IS NULL
                AND d."CertificateType" = 'NaturalCertificate'
                AND u."default_currency" = 'INR'
                AND d.brown = 0
                AND d.green = 0
                AND d.milky = 0
                AND d."eyeClean" = 100
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."OfferId" IS NULL
                AND d."availability" = 'AVAILABLE'
                AND nc.verified = 'true'
                AND qc."CertificateId" IS NULL
                AND geo_country = 'IN'
                AND cs.self_serve_po IS NULL
                ORDER BY NC."certNumber" DESC
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/multipleaddressstone2.json', result1.rows);
      });
    });
    it('Get credit note stone', { tags: '@DBCreditNote' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber"
                  FROM "NaturalCertificates" nc
                          INNER JOIN "Diamonds" d ON d."CertificateId" = nc."id"
                          INNER JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                          INNER JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                          LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                  WHERE u."role" = 'SUPPLIER'
                    AND u."default_currency" = 'INR'
                    AND u."verifyStatus" = '4'
                    AND d."OrderItemId" IS NULL
                    AND d."CertificateType" = 'NaturalCertificate'
                    AND d.brown = 0
                    AND d.green = 0
                    AND d.milky = 0
                    AND d."eyeClean" = 100
                    AND d."availability" = 'AVAILABLE'
                    AND nc.verified = 'true'
                    AND qc."CertificateId" IS NULL
                    AND cs."cert_details_enabled" = true
                    AND cs.self_serve_po IS NULL
                    AND cs.accept_returns = true
                    AND u.geo_country = 'IN'
                  ORDER BY cs."CompanyId"
                  LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/creditnotestone.json', result1.rows);
      });
    });
    it('Get source stone cert', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber", d."CertificateId"
                FROM "Diamonds" d
                JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                JOIN "NaturalCertificates" nc ON d."CertificateId" = nc.id
                JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                WHERE d."OrderItemId" IS NULL
                  AND d."CertificateType" = 'NaturalCertificate'
                  AND d."availability" = 'AVAILABLE'
                  AND cs.nivoda_express_supplier = true
                  AND d."FbnId" IS NULL
                  AND u."id" IN (
                    SELECT DISTINCT u2."id"
                    FROM "Diamonds" d2
                    JOIN "CompanySettings" cs2 ON cs2."CompanyId" = d2."CompanyId"
                    JOIN "Users" u2 ON u2."CompanyId" = cs2."CompanyId"
                    WHERE d2."OrderItemId" IS NULL
                      AND d2."CertificateType" = 'NaturalCertificate'
                      AND d2."availability" = 'AVAILABLE'
                      AND d2."FbnId" IS null
                  )
                  AND NOT EXISTS (
                    SELECT 1
                    FROM "ExpressRequestItems" eri
                    WHERE eri."CertificateId" = d."CertificateId"
                  )
                  LIMIT 3;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/sourcestonecert.json', result1.rows);
      });
    });
    it('Get not available source stone cert', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT "NaturalCertificates"."certNumber"  
        FROM "Diamonds"
        INNER JOIN "NaturalCertificates" ON "Diamonds"."CertificateId" = "NaturalCertificates"."id"
        WHERE "Diamonds"."OrderItemId" IS NOT NULL 
        AND "Diamonds"."CertificateType" = 'NaturalCertificate' 
        AND "Diamonds"."mine_of_origin" IS NOT NULL
        AND  "Diamonds"."availability" <> 'AVAILABLE'
        limit 3;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/notavailablesourcestonecert.json', result1.rows);
      });
    });
    it('Get accept source stone', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `select nc."certNumber"
        from "Diamonds" d 
        join "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
        join "NaturalCertificates" nc on d."CertificateId" = nc.id
      where 
        d."OrderItemId" IS NULL
        AND d."CertificateType" = 'NaturalCertificate'
        AND d."availability" = 'AVAILABLE'
        AND cs.nivoda_express_supplier=true
        AND d."FbnId" is null
        ORDER BY nc."certNumber" DESC
        limit 3;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/acceptsourcestone.json', result1.rows);
      });
    });
    it('Get nivoda express user', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email", c.name
                FROM "Users" u
                        INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                        INNER JOIN "Companies"  c on c.id=u."CompanyId"
                WHERE u."role" = 'CUSTOMER'
                  AND u."default_currency" = 'USD'
                  AND cs."cert_details_enabled" = true
                  AND cs."accept_holds" = true
                  AND u."verifyStatus" = '4'
                  AND u."status" = '4'
                  AND u."address_verified" = 'true'
                  AND u."LocationId" IS NOT NULL
                  AND u.geo_country is not null
                  AND cs."display_supplier_name" = true
                  AND cs.nivoda_express_enabled IS NOT NULL
                  AND cs.memo_enabled IS NULL 
                  AND cs.self_serve_po IS NULL 
                  AND code_disabled IS NULL
                LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/nivodaexpressuser.json', result1.rows);
      });
    });
    it('Get nivoda express stone', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT lc."certNumber"
                FROM "LabgrownCertificates" lc
                        INNER JOIN "Diamonds" d ON d."CertificateId" = lc."id"
                        INNER JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                        INNER JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                        INNER JOIN "OrderItems" oi ON oi."ProductId" = d.id
                        inner join "FbnItems" fi on fi.id = d."FbnId"
                  AND d."CertificateType" =  'LabgrownCertificate'
                  AND d."availability" = 'AVAILABLE'
                  AND cs.nivoda_express_supplier = true
                  AND cs."cert_details_enabled" = true
                  AND d."FbnId" is not null
                  AND oi."FbnType" = 'INTERNAL'
                  AND d."OrderItemId" is null
                  AND d."location" = 'e111071c-5af8-43b7-b8a3-f1799b9bc734'
                  AND fi.internal_status = 'ACTIVE'
                  AND cs.self_serve_po IS NULL
                LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/nivodaexpressstone.json', result1.rows);
      });
    });
    it('Get Credit Note Gems', { tags: '@DBCreditNote' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `WITH cart_gemstones AS (SELECT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "GemstoneId"
                        FROM "CartItems")
                SELECT DISTINCT nc."certNumber"
                FROM "Users" u
                        JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                        JOIN "Companies" c ON c.id = cs."CompanyId" AND c.supplier_status = 'LIVE'
                        JOIN "Gemstones" d
                              ON cs."CompanyId" = d."CompanyId" AND d."OrderItemId" IS NULL AND d.availability = 'AVAILABLE'
                        JOIN "GemCertificates" nc ON d."CertificateId" = nc."id"
                        LEFT JOIN cart_gemstones si ON d."id" = si."GemstoneId"
                WHERE u."role" = 'SUPPLIER'
                  AND u."default_currency" = 'INR'
                  AND u."verifyStatus" = '4'
                  AND cs."cert_details_enabled" = true
                  AND si."GemstoneId" IS NULL
                  AND cs."self_serve_po" IS NULL
                LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/creditNoteGems.json', result1.rows);
      });
    });
    it('Get credit Note Diamond', { tags: '@DBCreditNote' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber"
            FROM "NaturalCertificates" nc
                    INNER JOIN "Diamonds" d ON d."CertificateId" = nc."id"
                    INNER JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                    INNER JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                    LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
            WHERE u."role" = 'SUPPLIER'
              AND u."default_currency" = 'INR'
              AND u."verifyStatus" = '4'
              AND d."OrderItemId" IS NULL
              AND d."CertificateType" = 'NaturalCertificate'
              AND d.brown = 0
              AND d.green = 0
              AND d.milky = 0
              AND d."eyeClean" = 100
              AND d."availability" = 'AVAILABLE'
              AND nc.verified = 'true'
              AND qc."CertificateId" IS NULL
              AND cs."cert_details_enabled" = true
              AND cs.self_serve_po IS NULL
              AND cs.accept_returns = true
              AND u.geo_country = 'IN'
            ORDER BY cs."CompanyId" DESC
            LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/creditNoteDiamond.json', result1.rows);
      });
    });
    it('Get memo And Express User', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
             SELECT u."email" AS "email", c.name
            FROM "Users" u
                    INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                    INNER JOIN "Companies" c on c.id = u."CompanyId"
                    INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
            WHERE u."role" = 'CUSTOMER'
              AND cs.nivoda_express_enabled IS NOT null
              and u."OwnerOfCompanyId"  is not null
              AND u."status" = '4'
              and u."verifyStatus" = '4'
              AND u."address_verified" = true
              AND u."subtype" = 'PLATFORM_AND_CFM'
              AND ck."kyc_verified" = true
              AND cs.memo_enabled IS NOT null
            ORDER BY email desc
            LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/memoAndExpressUser.json', result1.rows);
      });
    });
    it('Get memo User', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email", c.name
                FROM "Users" u
                        INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                        INNER JOIN "Companies" c on c.id = u."CompanyId"
                WHERE u."role" = 'CUSTOMER'
                  AND cs.memo_enabled IS NOT NULL
                  and cs.nivoda_express_enabled IS NULL
                LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/memoUser.json', result1.rows);
      });
    });
    it('Get search Only Cert', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber", d."CertificateId"
                FROM "Diamonds" d
                JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                JOIN "NaturalCertificates" nc ON d."CertificateId" = nc.id
                JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                WHERE d."OrderItemId" IS NULL
                  AND d."CertificateType" = 'NaturalCertificate'
                  AND d."availability" = 'AVAILABLE'
                  AND cs.nivoda_express_supplier = true
                  AND d."FbnId" IS NULL
                  AND u."id" IN (
                    SELECT DISTINCT u2."id"
                    FROM "Diamonds" d2
                    JOIN "CompanySettings" cs2 ON cs2."CompanyId" = d2."CompanyId"
                    JOIN "Users" u2 ON u2."CompanyId" = cs2."CompanyId"
                    WHERE d2."OrderItemId" IS NULL
                      AND d2."CertificateType" = 'NaturalCertificate'
                      AND d2."availability" = 'AVAILABLE'
                      AND d2."FbnId" IS NULL
                  )
                  AND NOT EXISTS (
                    SELECT 1
                    FROM "ExpressRequestItems" eri
                    WHERE eri."CertificateId" = d."CertificateId"
                  )
                OFFSET 20 Limit 3;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/searchOnlyCert.json', result1.rows);
      });
    });
    it('Get accept the request', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber", d."CertificateId"
                FROM "Diamonds" d
                JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                JOIN "NaturalCertificates" nc ON d."CertificateId" = nc.id
                JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                WHERE d."OrderItemId" IS NULL
                  AND d."CertificateType" = 'NaturalCertificate'
                  AND d."availability" = 'AVAILABLE'
                  AND cs.nivoda_express_supplier = true
                  AND d."FbnId" IS NULL
                  AND u."id" IN (
                    SELECT DISTINCT u2."id"
                    FROM "Diamonds" d2
                    JOIN "CompanySettings" cs2 ON cs2."CompanyId" = d2."CompanyId"
                    JOIN "Users" u2 ON u2."CompanyId" = cs2."CompanyId"
                    WHERE d2."OrderItemId" IS NULL
                      AND d2."CertificateType" = 'NaturalCertificate'
                      AND d2."availability" = 'AVAILABLE'
                      AND d2."FbnId" IS null
                  )
                  AND NOT EXISTS (
                    SELECT 1
                    FROM "ExpressRequestItems" eri
                    WHERE eri."CertificateId" = d."CertificateId"
                  )
                  LIMIT 3 OFFSET 40;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/accepttherequest.json', result1.rows);
      });
    });
    it('Express Stones', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT distinct (lc."certNumber")
                FROM "LabgrownCertificates" lc
                        INNER JOIN "Diamonds" d ON d."CertificateId" = lc."id"
                        INNER JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                        INNER JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                        INNER JOIN "OrderItems" oi ON oi."ProductId" = d.id
                        INNER JOIN "FbnItems" fi on fi.id = d."FbnId"
                    AND d."CertificateType" = 'LabgrownCertificate'
                    AND d."availability" = 'AVAILABLE'
                    AND cs.nivoda_express_supplier = true
                    AND cs."cert_details_enabled" = true
                    AND d."FbnId" is not null
                    AND oi."FbnType" = 'INTERNAL'
                    AND d."OrderItemId" is null
                    AND d."location" = 'e111071c-5af8-43b7-b8a3-f1799b9bc734'
                    AND fi.internal_status = 'ACTIVE'
                    AND cs.self_serve_po IS NULL
                ORDER BY lc."certNumber" ASC
                LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/expressCheckoutStone1.json', result1.rows);
      });
    });
    it('Express Stones', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT distinct (lc."certNumber"), cs."CompanyId"
                FROM "LabgrownCertificates" lc
                        INNER JOIN "Diamonds" d ON d."CertificateId" = lc."id"
                        INNER JOIN "CompanySettings" cs ON cs."CompanyId" = d."CompanyId"
                        INNER JOIN "Users" u ON u."CompanyId" = cs."CompanyId"
                        INNER JOIN "OrderItems" oi ON oi."ProductId" = d.id
                        inner join "FbnItems" fi on fi.id = d."FbnId"
                    AND d."CertificateType" = 'LabgrownCertificate'
                    AND d."availability" = 'AVAILABLE'
                    AND cs.nivoda_express_supplier = true
                    AND cs."cert_details_enabled" = true
                    AND d."FbnId" is not null
                    AND oi."FbnType" = 'INTERNAL'
                    AND d."OrderItemId" is null
                    AND d."location" = 'e111071c-5af8-43b7-b8a3-f1799b9bc734'
                    AND fi.internal_status = 'ACTIVE'
                    AND cs.self_serve_po IS NULL
                ORDER BY CS."CompanyId" DESC
                LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/expressCheckoutStone2.json', result1.rows);
      });
    });
    it('Credit Notes Finance Dashboard Data', { tags: '@Finances' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
      SELECT q.email,
       q.invoice_number,
       TO_CHAR(q.usd_total, 'FM$999,999,990.00') AS usd_total
        FROM (SELECT u.email,
                    i.invoice_number,
                    ROUND(i.total_in_local_currency / i.exchange_rate, 2) AS usd_total,
                    i.due_date
              FROM "Invoices" i
                      JOIN "Users" u ON i."to" = u."CompanyId"
              WHERE i.invoice_number LIKE 'CN%'
                AND (i.status = 'paid' OR i.status IS NULL)) q
        WHERE q.usd_total > 1
        ORDER BY ABS(EXTRACT(EPOCH FROM (q.due_date - NOW())))
        LIMIT 1;
    `
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/creditNoteFinances.json', result1.rows);
      });
    });

    it('All Tab Finance Dashboard Data', { tags: '@Finances' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u.email,
       i.invoice_number,
       TO_CHAR(ROUND(i.total_in_local_currency / i.exchange_rate, 2), 'FM$999,999,990.00') AS usd_total,
       TO_CHAR(ROUND(i.settled_amount / i.exchange_rate, 2), 'FM$999,999,990.00')          AS settled_amount,
       INITCAP(i.status)                                                                   AS status
      FROM "Invoices" i
              INNER JOIN "Users" u ON i."to" = u."CompanyId"
      WHERE i.status = 'PAID'
      ORDER BY ABS(EXTRACT(EPOCH FROM (i.due_date - NOW())))
      LIMIT 1;
`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/AllTabFinancesData.json', result1.rows);
      });
    });
    it('Overdue Invoices Finance Dashboard Data', { tags: '@Finances' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `select u.email,
              invoice_number,
              to_char(Round(total_in_local_currency / exchange_rate, 2), 'FM$999,999,990.00') as "usd_total",
              to_char(Round(settled_amount, 2), 'FM$999,999,990.00')                          as "settled_amount",
              i.status
              from "Invoices" i
                      inner join "Users" u on i."to" = u."CompanyId"
              where i.status is null
                and settled is false
                and u.status = '4'
                and due_date is not null
                and  u."role" = 'CUSTOMER'
                and due_date < now()
                AND u."verifyStatus" = '4'
              order by email
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/overdueFinancesData.json', result1.rows);
      });
    });
    it('Kriya User With Account Limit', { tags: '@Finances' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u.email,
              kriya_company_identifier,
              cf."DEFAULT_CHECKOUT_OPTION",
              CASE
                  WHEN cf."INVOICE_CURRENCY" = 'EUR' THEN
                      REPLACE(
                              REPLACE(
                                      REPLACE(
                                              TO_CHAR(ROUND("MARKET_PAY_LIMIT" - "MARKET_PAY_LIMIT_CONSUMED", 2),
                                                      '****************'),
                                              ',', 'X'
                                      ),
                                      '.', ','
                              ),
                              'X', '.'
                      ) || ' €'
           WHEN cf."INVOICE_CURRENCY" = 'GBP' THEN
               TO_CHAR(ROUND("MARKET_PAY_LIMIT" - "MARKET_PAY_LIMIT_CONSUMED", 2), 'FM£999,999,990.00')
           WHEN cf."INVOICE_CURRENCY" = 'USD' THEN
               TO_CHAR(ROUND("MARKET_PAY_LIMIT" - "MARKET_PAY_LIMIT_CONSUMED", 2), 'FM$999,999,990.00')
           ELSE
               TO_CHAR(ROUND("MARKET_PAY_LIMIT" - "MARKET_PAY_LIMIT_CONSUMED", 2), 'FM999,999,990.00')
           END AS "Total_Account_Limit"
            FROM "CompanyFinanceSettings" cf
                    INNER JOIN "Companies" c ON cf."CompanyId" = c.id
                    INNER JOIN "Users" u ON cf."CompanyId" = u."CompanyId"
            WHERE "MARKET_PAY_ACTIVE" = TRUE
              AND "MARKET_PAY_ELIGIBLE" = TRUE
              AND kriya_company_identifier IS NOT NULL
              AND "MARKET_PAY_LIMIT" > 1000
              AND cf."DISABLE_CHECKOUT" = FALSE
              AND cf."DEFAULT_CHECKOUT_OPTION" = 'ip_payment'
              AND cf."ALL_IN_PRICING" = TRUE
              AND cf."DISABLE_CHECKOUT" = FALSE
              AND c.code_disabled IS NULL
              AND u."verifyStatus" = 4
              AND u."role" = 'CUSTOMER'
              AND u."status" = '4'
              AND u."address_verified" = 'true'
            ORDER BY u.email DESC
            LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/kriyaUser.json', result1.rows);
      });
    });
    it('Get Cert Number', { tags: '@DBRemainingCases' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT "NaturalCertificates"."certNumber"  
      FROM "Diamonds"
      INNER JOIN "NaturalCertificates" ON "Diamonds"."CertificateId" = "NaturalCertificates"."id"
      WHERE "Diamonds"."OrderItemId" IS NULL 
      AND "Diamonds"."CertificateType" = 'NaturalCertificate' 
      AND "Diamonds"."mine_of_origin" IS NOT NULL
      AND  "Diamonds"."availability" = 'AVAILABLE'
      AND "NaturalCertificates"."ImageId"  is not null

      AND "Diamonds"."CompanyId" Not IN (
          SELECT "SupplierId" FROM "VolumeDiscounts"
      )
      limit 1`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/certimage.json', result1.rows);
      });
    });
    it('Get Labgrown Cert Number', { tags: '@DBRemainingCases' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `WITH filtered_data AS (SELECT lc."certNumber"
                       FROM "Users" u
                                INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                                INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                                INNER JOIN "LabgrownCertificates" lc ON d."CertificateId" = lc."id"
                                LEFT JOIN (SELECT DISTINCT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "labId"
                                           FROM "CartItems") si ON d."id" = si."labId"
                       WHERE u."role" = 'SUPPLIER'
                         AND d."OrderItemId" IS NULL
                         AND d."CertificateType" = 'LabgrownCertificate'
                         AND u."default_currency" = 'USD'
                         AND cs."cert_details_enabled" = true
                         AND u."verifyStatus" = '4'
                         AND si."labId" IS NULL
                         AND lc.verified = 'true'
                         AND d.availability = 'AVAILABLE'
                         and lc."ImageId" is not null
                       LIMIT 100
                        )
                        SELECT "certNumber"
                        FROM filtered_data
                        ORDER BY RANDOM()
                        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/labgrowncertimage.json', result1.rows);
      });
    });
    it('Stripe And Airwallex User With Due 30Day Invoices', { tags: '@Finances' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `select u.email,
                invoice_number,
                Round(total_in_local_currency / exchange_rate, 2) USD_TOTAL,
                total_in_local_currency,
                invoiced_at,
                due_date,
                i.status
                from "Invoices" i
                        inner join "Users" u on i."to" = u."CompanyId"
                        inner join "StripeCustomerSettings" scs on scs."CompanyId" = u."CompanyId"
                        inner join "AirwallexCompanySettings" acs on acs."CompanyId" = u."CompanyId"
                        inner join "CompanyFinanceSettings" cfs on cfs."CompanyId" = u."CompanyId"
                where i.status is null
                  and settled is false
                  and due_date is not null
                  and scs."IsEnabled" = true
                  and acs.enable_airwallex = true
                  and cfs."DEFAULT_CHECKOUT_OPTION" = 'cr_30_payment'
                order by i."createdAt" asc 
                LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/stripeAndAirwallexUserWith30DayInvoice.json', result1.rows);
      });
    });
    it('Airwallex User With Due Upfront Invoice', { tags: '@Finances' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `select u.email,
              cfs."DEFAULT_CHECKOUT_OPTION",
              invoice_number,
              Round(total_in_local_currency / exchange_rate, 2) USD_TOTAL,
              total_in_local_currency,
              invoiced_at,
              due_date,
              i.status
              from "Invoices" i
                      inner join "Users" u on i."to" = u."CompanyId"
                      inner join "StripeCustomerSettings" scs on scs."CompanyId" = u."CompanyId"
                      inner join "AirwallexCompanySettings" acs on acs."CompanyId" = u."CompanyId"
                      inner join "CompanyFinanceSettings" cfs on cfs."CompanyId" = u."CompanyId"
              where i.status is null
                and scs."IsEnabled" = false
                and acs.enable_airwallex = true
                and cfs."DEFAULT_CHECKOUT_OPTION" = 'up_front_payment'
                and i.status is null
                and settled is false
                and due_date is not null
              order by invoiced_at desc
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/airwallexUpfrontUser.json', result1.rows);
      });
    });
    it('Airwallex Stripe Disabled User With Due Upfront Invoice', { tags: '@Finances' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `select u.email,
                cfs."DEFAULT_CHECKOUT_OPTION",
                invoice_number,
                Round(total_in_local_currency / exchange_rate, 2) USD_TOTAL,
                total_in_local_currency,
                invoiced_at,
                due_date,
                i.status
                from "Invoices" i
                        inner join "Users" u on i."to" = u."CompanyId"
                        inner join "StripeCustomerSettings" scs on scs."CompanyId" = u."CompanyId"
                        inner join "AirwallexCompanySettings" acs on acs."CompanyId" = u."CompanyId"
                        inner join "CompanyFinanceSettings" cfs on cfs."CompanyId" = u."CompanyId"
                where i.status is null
                  and scs."IsEnabled" = false
                and acs.enable_airwallex = false
                  and cfs."DEFAULT_CHECKOUT_OPTION" = 'up_front_payment'
                  and i.status is null
                  and settled is false
                  and due_date is not null
                order by invoiced_at
                LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/airwallexStripDisabledUser.json', result1.rows);
      });
    });
    it('Total Overdue For A User', { tags: '@Finances' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
        WITH UserCurrencyCounts AS (
    SELECT 
        "to" AS CompanyId,
        COUNT(DISTINCT currency) AS currency_count
    FROM 
        "Invoices"
    WHERE 
        settled = FALSE
        AND due_date IS NOT NULL
        AND due_date < NOW()
        AND invoice_sent IS NOT NULL
    GROUP BY 
        "to"
)
SELECT 
    u.email,
    TO_CHAR(
        TRUNC(
            SUM(
                CASE
                    WHEN uc.currency_count > 2 THEN
                        CASE
                            WHEN i.status = 'PARTIAL_PAID'
                                THEN (i.total_in_local_currency / i.exchange_rate) - i.settled_amount
                            WHEN i.status IS NULL THEN i.total_in_local_currency / i.exchange_rate
                            ELSE 0
                        END
                    ELSE
                        CASE
                            WHEN i.status = 'PARTIAL_PAID'
                                THEN i.total_in_local_currency - i.settled_amount
                            WHEN i.status IS NULL THEN i.total_in_local_currency
                            ELSE 0
                        END
                END
            ), 2 -- Set precision to 2 decimal places
        ), 'FM999,999,999.00' -- Format to two decimal places
    ) AS total_overdue_amount
FROM 
    "Invoices" i
    INNER JOIN "Users" u ON i."to" = u."CompanyId"
    INNER JOIN UserCurrencyCounts uc ON i."to" = uc.CompanyId
WHERE 
    (i.status = 'PARTIAL_PAID' OR i.status IS NULL)
    AND i.settled = FALSE
    AND i.due_date IS NOT NULL
    AND i.due_date < NOW()
    AND invoice_sent IS NOT NULL
GROUP BY 
    u.email, uc.currency_count
HAVING 
    uc.currency_count >= 2
 LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/totalOverdue.json', result1.rows);
      });
    });
    it('Unpaid Tab Data For A User', { tags: '@Finances' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u.email,u."CompanyId", invoice_sent, proforma_pdf,
                    invoice_number,due_date,
                    CASE
                        WHEN ROUND(total_in_local_currency / exchange_rate, 2) > 1000
                            THEN TO_CHAR(ROUND(total_in_local_currency / exchange_rate, 2), 'FM999,999,990.00')
                        ELSE ROUND(total_in_local_currency / exchange_rate, 2)::TEXT
                        END AS USD_TOTAL
              FROM "Invoices" i
                      INNER JOIN "Users" u ON i."to" = u."CompanyId"
              WHERE i.status IS NULL
                AND settled = FALSE
                AND due_date IS NOT NULL
              AND invoice_sent IS not null
                AND u.email = '<EMAIL>'
              ORDER BY due_date desc
LIMIT 4;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/UnpaidTabData.json', result1.rows);
      });
    });
    it('Login As MemoEnabled User', { tags: '@DBMemo' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
      SELECT 
        u."email" AS "email",
        cs.memo_duration, 
        c."name" , cs.memo_stone_checkout_limit as count
      FROM 
        "Users" u
      INNER JOIN 
        "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN 
        "Companies" c ON u."CompanyId" = c.id 
      INNER JOIN 
        "CompanyFinanceSettings" cfs on c.id = cfs."CompanyId" 
      INNER JOIN 
      	"Locations" l on c.id = l."CompanyId"   
      WHERE 
        u."role" = 'CUSTOMER'
        AND u."verifyStatus" = '4'
        AND u."status" = '4'
        AND u."LocationId" IS NOT NULL
        AND u.geo_country is not null
        AND cs.memo_enabled IS NOT NULL
        AND cs.nivoda_express_enabled IS NOT NULL
        AND cs."cert_details_enabled" = true
        AND l.country = 'US'
        AND l.default_shipping_address = true
        AND u.email NOT IN ('<EMAIL>','<EMAIL>')
        AND cfs."CREDIT_LIMIT" > 10000
        and cfs. "DISABLE_CHECKOUT"  is False

      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/LoginasMemoEnabledUser.json', result1.rows);
      });
    });

    it('Get Melee SR Numbers', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `select order_request_number
              from "SpecialRequests"
              where "closedAt" is null
                and status = 'OPEN'
              order by "createdAt" desc;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/meleeSrData.json', result1.rows);
      });
    });

    it('Get Accepted Melee SR', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT sr.order_request_number
              FROM   "SpecialRequests" sr
                      INNER JOIN "OrderRequests" or2
                              ON sr.order_request_number = or2.order_request_number
              WHERE  sr.status = 'ACCEPTED'
                     AND or2.status = 'ACCEPTED'
              ORDER  BY sr."createdAt" DESC`
      }).then((result) => {
        cy.writeFile('cypress/fixtures/acceptedMeleeSrData.json', result.rows);
      });
    });

    it('Get Rejected Melee SR', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT sr.order_request_number
              FROM   "SpecialRequests" sr
                      INNER JOIN "OrderRequests" or2
                              ON sr.order_request_number = or2.order_request_number
              WHERE  sr.status = 'REJECTED'
                     AND or2.status = 'REJECTED'
              ORDER  BY sr."createdAt" DESC`
      }).then((result) => {
        cy.writeFile('cypress/fixtures/rejectedMeleeSrData.json', result.rows);
      });
    });

    it('Get Order Placed Melee SR', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT sr.order_request_number
              FROM   "SpecialRequests" sr
                      INNER JOIN "OrderRequests" or2
                              ON sr.order_request_number = or2.order_request_number
              WHERE  sr.status = 'ORDER_PLACED'
                     AND or2.status = 'ORDER_PLACED'
              ORDER  BY sr."createdAt" DESC`
      }).then((result) => {
        cy.writeFile('cypress/fixtures/orderPlacedMeleeSrData.json', result.rows);
      });
    });

    it('Get Expired Melee SR', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT sr.order_request_number
              FROM   "SpecialRequests" sr
              WHERE  sr.status = 'EXPIRED'
              ORDER  BY sr."createdAt" DESC`
      }).then((result) => {
        cy.writeFile('cypress/fixtures/expiredMeleeSrData.json', result.rows);
      });
    });

    it('Get Cancelled Melee SR', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT sr.order_request_number
              FROM   "SpecialRequests" sr
              WHERE  sr.status = 'CANCELLED'
              ORDER  BY sr."createdAt" DESC`
      }).then((result) => {
        cy.writeFile('cypress/fixtures/cancelledMeleeSrData.json', result.rows);
      });
    });

    it('Get Customer with Melee SR Quotations', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
        SELECT u.email
        FROM   "SpecialRequests" sr
               INNER JOIN "Users" u
                      ON sr.requester_id = u.id
        WHERE  customer_order_number IS NULL
               AND sr.status = 'QUOTE_SENT'
        ORDER  BY sr."createdAt" DESC 
        LIMIT 1;`
      }).then((result) => {
        cy.writeFile('cypress/fixtures/meleeQuotationCustomer.json', result.rows);
      });
    });

    it('Get Customer with Melee SR Quotation as Accepted', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
        SELECT u.email
        FROM   "SpecialRequests" sr
               INNER JOIN "Users" u
                      ON sr.requester_id = u.id
        WHERE  customer_order_number IS NULL
               AND sr.status = 'ACCEPTED'
        ORDER  BY sr."createdAt" DESC 
        LIMIT 1;`
      }).then((result) => {
        cy.writeFile('cypress/fixtures/meleeSrAcceptedCustomer.json', result.rows);
      });
    });

    it('Get Customer with Melee SR Quotation as Rejected', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
        SELECT u.email
        FROM   "SpecialRequests" sr
               INNER JOIN "Users" u
                      ON sr.requester_id = u.id
        WHERE  customer_order_number IS NULL
               AND sr.status = 'REJECTED'
        ORDER  BY sr."createdAt" DESC 
        LIMIT 1;`
      }).then((result) => {
        cy.writeFile('cypress/fixtures/meleeSrRejectedCustomer.json', result.rows);
      });
    });

    it('Get Customer with Melee SR as Order Placed', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
        SELECT u.email
        FROM   "SpecialRequests" sr
               INNER JOIN "Users" u
                      ON sr.requester_id = u.id
        WHERE  customer_order_number IS NULL
               AND sr.status = 'ORDER_PLACED'
        ORDER  BY sr."createdAt" DESC 
        LIMIT 1;`
      }).then((result) => {
        cy.writeFile('cypress/fixtures/meleeSrOrderPlacedCustomer.json', result.rows);
      });
    });

    it('Get Customer with Melee SR as Expired', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
        SELECT u.email
        FROM   "SpecialRequests" sr
               INNER JOIN "Users" u
                      ON sr.requester_id = u.id
        WHERE  customer_order_number IS NULL
               AND sr.status = 'EXPIRED'
        ORDER  BY sr."createdAt" DESC 
        LIMIT 1;`
      }).then((result) => {
        cy.writeFile('cypress/fixtures/meleeSrExpiredCustomer.json', result.rows);
      });
    });

    it('Get Customer with Melee SR as Cancelled', { tags: '@DBSr' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
        SELECT u.email
        FROM   "SpecialRequests" sr
               INNER JOIN "Users" u
                      ON sr.requester_id = u.id
        WHERE  customer_order_number IS NULL
               AND sr.status = 'CANCELLED'
        ORDER  BY sr."createdAt" DESC 
        LIMIT 1;`
      }).then((result) => {
        cy.writeFile('cypress/fixtures/meleeSrCancelledCustomer.json', result.rows);
      });
    });

    it('Login As MemoEnabled User Who is not company Owner', { tags: '@DBMemo' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
      SELECT 
        u."email" AS "email", 
        cs.memo_duration, 
        c."name" , cs.memo_enabled , u."OwnerOfCompanyId" , cfs."CREDIT_LIMIT" , ROUND((cfs."CR_60_PER") * 100, 2) AS "percentage"
      FROM 
        "Users" u
      INNER JOIN 
        "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN 
        "Companies" c ON u."CompanyId" = c.id 
      INNER JOIN 
        "CompanyFinanceSettings" cfs on c.id = cfs."CompanyId" 
      INNER JOIN 
      	"Locations" l on c.id = l."CompanyId"   
      WHERE 
        u."role" = 'CUSTOMER'
        AND u."verifyStatus" = '4'
        AND u."status" = '4'
        AND u."LocationId" IS NOT NULL
       AND u.geo_country is not null
        AND cs.memo_enabled IS NOT NULL
        AND cs.nivoda_express_enabled IS NOT NULL
        AND cs."cert_details_enabled" = true
        AND l.country = 'US'
        AND l.default_shipping_address = true
        AND cs."cert_details_enabled" = true
        and cfs."CREDIT_LIMIT" > 10000
        and cfs. "DISABLE_CHECKOUT"  is False
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/LoginasMemoEnabledUserNonCompanyUser.json', result1.rows);
      });
    });
    it('Login As MemoEnabled User Who is company Owner', { tags: '@DBMemo' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
      SELECT 
        u."email" AS "email", 
        cs.memo_duration, 
        c."name" , cs.memo_enabled , u."OwnerOfCompanyId" , cfs."CREDIT_LIMIT" , ROUND((cfs."CR_60_PER" - cfs."CR_30_PER") * 100, 2) AS "percentage"
      FROM 
        "Users" u
      INNER JOIN 
        "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN 
        "Companies" c ON u."CompanyId" = c.id 
      INNER JOIN 
        "CompanyFinanceSettings" cfs on c.id = cfs."CompanyId" 
      INNER JOIN 
      	"Locations" l on c.id = l."CompanyId"   
      WHERE 
        u."role" = 'CUSTOMER'
        AND u."verifyStatus" = '4'
        AND u."status" = '4'
        AND u.geo_country is not null
        AND cs.memo_enabled IS NOT NULL
        AND cs.nivoda_express_enabled IS NOT NULL
        AND cs."cert_details_enabled" = true
        AND l.country = 'US'
        AND l.default_shipping_address = true
        AND cs."cert_details_enabled" = true
        and cfs."CREDIT_LIMIT" > 10000
        and cfs. "DISABLE_CHECKOUT"  is False
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/LoginasMemoEnabledUserCompanyOwner.json', result1.rows);
      });
    });
    it('Login As MemoEnabled User Who is company Owner', { tags: '@DBMemo' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
      SELECT 
        u."email" AS "email", 
        cs.memo_duration, 
        c."name" , cs.memo_enabled , u."OwnerOfCompanyId" , cfs."CREDIT_LIMIT" , ROUND((cfs."CR_60_PER" - cfs."CR_30_PER") * 100, 2) AS "percentage"
      FROM 
        "Users" u
      INNER JOIN 
        "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN 
        "Companies" c ON u."CompanyId" = c.id 
      INNER JOIN 
        "CompanyFinanceSettings" cfs on c.id = cfs."CompanyId" 
      INNER JOIN 
      	"Locations" l on c.id = l."CompanyId"  
      WHERE 
        u."role" = 'CUSTOMER'
        AND u."verifyStatus" = '4'
        AND u."status" = '4'
        AND u."LocationId" IS NOT NULL
        AND u.geo_country is not null
        AND cs.memo_enabled IS NOT NULL
        AND cs.nivoda_express_enabled IS NOT NULL
        AND cs."cert_details_enabled" = true
        AND l.country = 'US'
        AND l.default_shipping_address = true
        AND cs."cert_details_enabled" = true
        and cfs."CREDIT_LIMIT" < 1000
        and cfs. "DISABLE_CHECKOUT"  is False
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/LessCreditLimitMemoEnabledUser.json', result1.rows);
      });
    });
    it('Login As MemoEnabled User Without All in One Pricing', { tags: '@DBMemo' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
      SELECT 
        u."email" AS "email", 
        cs.memo_duration, 
        c."name" , cs.memo_enabled , u."OwnerOfCompanyId" , cfs."CREDIT_LIMIT" , ROUND((cfs."CR_60_PER" - cfs."CR_30_PER") * 100, 2) AS "percentage"
      FROM 
        "Users" u
      INNER JOIN 
        "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN 
        "Companies" c ON u."CompanyId" = c.id 
        inner join 
        "CompanyFinanceSettings" cfs on c.id = cfs."CompanyId" 
      INNER JOIN 
      	"Locations" l on c.id = l."CompanyId"    
      WHERE 
        u."role" = 'CUSTOMER'
        AND u."verifyStatus" = '4'
        AND u."status" = '4'
        AND u."LocationId" IS NOT NULL
        AND u.geo_country IS NOT null
        AND cs.memo_enabled IS NOT NULL
        AND cs.nivoda_express_enabled IS NOT NULL
        AND cs."cert_details_enabled" = true
        AND l.country = 'US'
        AND l.default_shipping_address = true
        and cfs."CREDIT_LIMIT" > 10000
        and cfs."ALL_IN_PRICING" = false
        and cfs. "DISABLE_CHECKOUT"  is False
        LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/memoEnabledWithOutAIP.json', result1.rows);
      });
    });

    it('Melee Stone Data', { tags: '@DBProduct' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT d."supplierStockId" AS "NivodaStockId"
              FROM "Users" u
                      JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      JOIN "MeleeCollections" d ON cs."CompanyId" = d."CompanyId"
                      LEFT JOIN (SELECT substring("OfferId" FROM position('/' IN "OfferId") + 1)::uuid AS "MeleeId"
                                  FROM "CartItems") si ON d."id" = si."MeleeId"
              WHERE d."OrderItemId" IS NULL
                AND d."IsParentMeleeCollection" IS NULL
                AND d."ParentMeleeCollectionId" IS NULL
                AND d."type" = 'NATURAL'
                AND d.pieces IS NULL
                AND u."default_currency" = 'USD'
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."MeleeId" IS NULL
                AND d.status = 'LIVE'
                AND source IN ('CONSIGNMENT', 'SOURCE_TO_ORDER')
              ORDER BY RANDOM()
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/meleeStoneData.json', result1.rows);
      });
    });

    it('Meemo Not Returnable Stone Data', { tags: '@DBExpress' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `select d."supplierStockId" AS "certNumber"
              from "FbnItems" fi
                      inner join "OrderItems" oi on oi."FbnId" = fi.id
                      inner join "Diamonds" d on d.id = oi."ProductId"
              where internal_status = 'ACTIVE'
                and oi."FbnType" = 'INTERNAL'
                and oi."status" = 'DELIVERED'
                and d."CertificateType" = 'NaturalCertificate'
                and d.availability = 'AVAILABLE'
                and fi.sourcing_type = 'MEMO'
                and oi.return_option = 'false'
              limit 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/memoNotReturnableStone.json', result1.rows);
      });
    });

    it('Delivery Timelines Data', { tags: '@DBDeliveryData' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT origin_country,
              destination_country,
              CONCAT(min_delivery_days, '-', max_delivery_days) AS delivery_days
        FROM "DeliveryTimelines"
        WHERE express_timeline IS NULL
          AND (
    (origin_country = 'US' AND destination_country = 'US')
        OR (origin_country = 'GB' AND destination_country = 'R')
        OR (origin_country = 'IN' AND destination_country = 'US')
        OR (origin_country = 'HK' AND destination_country = 'R')
        OR (origin_country = 'US' AND destination_country = 'R')
        OR (origin_country = 'GB' AND destination_country = 'GB')
        OR (origin_country = 'IN' AND destination_country = 'GB')
        OR (origin_country = 'HK' AND destination_country = 'R')
        OR (origin_country = 'US' AND destination_country = 'AU')
        OR (origin_country = 'GB' AND destination_country = 'AU')
        OR (origin_country = 'IN' AND destination_country = 'AU')
        OR (origin_country = 'HK' AND destination_country = 'AU')
        OR (origin_country = 'AU' AND destination_country = 'R')
    );`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/deliveryTimelineData.json', result1.rows);
      });
    });

    it('GB User Email', { tags: '@DBDeliveryData' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email"
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
              WHERE u."role" = 'CUSTOMER'
                AND u."verifyStatus" = '4'
                AND u."status" = '4'
                AND u."address_verified" = 'true'
                AND u."LocationId" IS NOT NULL
                AND geo_country = 'GB'
                AND default_currency = 'GBP'
                AND u.subtype = 'PLATFORM_AND_CFM'
                AND cs."display_supplier_name" = true
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/gbUuser.json', result1.rows);
      });
    });

    it('AU User Email', { tags: '@DBDeliveryData' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email"
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
              WHERE u."role" = 'CUSTOMER'
                AND u."verifyStatus" = '4'
                AND u."status" = '4'
                AND u."address_verified" = 'true'
                AND u."LocationId" IS NOT NULL
                AND geo_country = 'AU'
                AND default_currency = 'AUD'
                AND u.subtype = 'PLATFORM_AND_CFM'
                AND cs."display_supplier_name" = true
                order by u.email
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/auUser.json', result1.rows);
      });
    });

    it('Get IN Stone', { tags: '@DBDeliveryData' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber", u."CompanyId", U.geo_country
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                      LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                                  FROM "CartItems") si ON d."id" = si."DiamondId"
              WHERE u."role" = 'SUPPLIER'
                AND d."OrderItemId" IS NULL
                AND d."CertificateType" = 'NaturalCertificate'
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."OfferId" IS NULL
                AND d."availability" = 'AVAILABLE'
                AND u.geo_country = 'IN'
                AND default_currency = 'INR'
                AND NOT EXISTS (SELECT 1
                                FROM "DeliveryTimelinesExceptions" dte
                                WHERE cs."CompanyId" = dte."CompanyId")
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/getINstone.json', result1.rows);
      });
    });

    it('Get Us Stone', { tags: '@DBDeliveryData' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber", u."CompanyId", U.geo_country
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                      LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                                  FROM "CartItems") si ON d."id" = si."DiamondId"
              WHERE u."role" = 'SUPPLIER'
                AND d."OrderItemId" IS NULL
                AND d."CertificateType" = 'NaturalCertificate'
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."OfferId" IS NULL
                AND d."availability" = 'AVAILABLE'
                AND u.geo_country = 'US'
                AND default_currency = 'USD'
                AND NOT EXISTS (SELECT 1
                                FROM "DeliveryTimelinesExceptions" dte
                                WHERE cs."CompanyId" = dte."CompanyId")
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/getUSstone.json', result1.rows);
      });
    });

    it('Get GB Stone', { tags: '@DBDeliveryData' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber", u."CompanyId", U.geo_country
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                      LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                                  FROM "CartItems") si ON d."id" = si."DiamondId"
              WHERE u."role" = 'SUPPLIER'
                AND d."OrderItemId" IS NULL
                AND d."CertificateType" = 'NaturalCertificate'
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."OfferId" IS NULL
                AND d."availability" = 'AVAILABLE'
                AND u.geo_country = 'GB'
                AND default_currency = 'GBP'
                AND NOT EXISTS (SELECT 1
                                FROM "DeliveryTimelinesExceptions" dte
                                WHERE cs."CompanyId" = dte."CompanyId")
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/getGBstone.json', result1.rows);
      });
    });

    it('Get HK Stone', { tags: '@DBDeliveryData' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT nc."certNumber", u."CompanyId", U.geo_country
              FROM "Users" u
                      INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                      INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                      INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                      LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                      LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                                  FROM "CartItems") si ON d."id" = si."DiamondId"
              WHERE u."role" = 'SUPPLIER'
                AND d."OrderItemId" IS NULL
                AND d."CertificateType" = 'NaturalCertificate'
                AND cs."cert_details_enabled" = true
                AND u."verifyStatus" = '4'
                AND si."OfferId" IS NULL
                AND u.geo_country = 'HK'
                AND default_currency = 'HKD'
                AND NOT EXISTS (SELECT 1
                                FROM "DeliveryTimelinesExceptions" dte
                                WHERE cs."CompanyId" = dte."CompanyId")
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/getHKstone.json', result1.rows);
      });
    });
    it('Get Natural Diamond Supplier', () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
      SELECT 
          DISTINCT u."email" AS "email", 
                  c.type_detail,
                  c.name,
                  c.id,
                  c.api_type
      FROM 
          "Users" u
      INNER JOIN 
          "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN 
          "Companies" c ON c.id = u."CompanyId"
      WHERE 
          u."role" = 'SUPPLIER'
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = TRUE
          AND cs."accept_holds" = TRUE
          AND u."verifyStatus" = '4'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."LocationId" IS NOT NULL
          AND u.geo_country IS NOT NULL
          AND cs."display_supplier_name" = TRUE
          AND REGEXP_REPLACE(c.type_detail, '\\s+', ' ', 'g') LIKE '%Natural Diamond%'
      LIMIT 1;
    `
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/NaturalDiamondSupplier.json', result1.rows);
      });
    });
    it('Get Labgrown Diamond Supplier', () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
      SELECT 
          DISTINCT u."email" AS "email", 
                  c.type_detail,
                  c.name,
                  c.id,
                  c.api_type
      FROM 
          "Users" u
      INNER JOIN 
          "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN 
          "Companies" c ON c.id = u."CompanyId"
      WHERE 
          u."role" = 'SUPPLIER'
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = TRUE
          AND cs."accept_holds" = TRUE
          AND u."verifyStatus" = '4'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."LocationId" IS NOT NULL
          AND u.geo_country IS NOT NULL
          AND cs."display_supplier_name" = TRUE
          AND REGEXP_REPLACE(c.type_detail, '\\s+', ' ', 'g') LIKE '%Labgrown Diamond%'
      LIMIT 1;
    `
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/LabGrownDiamondSupplier.json', result1.rows);
      });
    });
    it('Get Gemstone Supplier', () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `
      SELECT 
          DISTINCT u."email" AS "email", 
                  c.type_detail,
                  c.name,
                  c.id,
                  c.api_type
      FROM 
          "Users" u
      INNER JOIN 
          "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
      INNER JOIN 
          "Companies" c ON c.id = u."CompanyId"
      WHERE 
          u."role" = 'SUPPLIER'
          AND cs."cert_details_enabled" = TRUE
          AND cs."accept_holds" = TRUE
          AND u."verifyStatus" = '4'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND cs."display_supplier_name" = TRUE
          AND REGEXP_REPLACE(c.type_detail, '\\s+', ' ', 'g') LIKE '%Gemstone%'
      LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/gemstonesupplier.json', result1.rows);
      });
    });

    it('Get Company For CFS', { tags: '@DBCfs' }, () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u.email,
                  c."name"                                                                             AS name,
                  c.website,
                  cf."ACCOUNT_LIMIT_CONSUMED",
                  '$' || TO_CHAR(cf."ACCOUNT_LIMIT" - cf."ACCOUNT_LIMIT_CONSUMED", 'FM999,999,999.00') AS "Total_Account_Limit"
            FROM "CompanyFinanceSettings" cf
                    INNER JOIN "Users" u ON u."CompanyId" = cf."CompanyId"
                    INNER JOIN "Companies" c ON u."CompanyId" = c.id
                    INNER JOIN "CompanyKycs" ck ON ck."CompanyId" = u."CompanyId"
            WHERE cf."ADVANCE_PAYMENT" = false
              AND u."verifyStatus" = 4
              AND u."role" = 'CUSTOMER'
              AND u."status" = '4'
              AND u."address_verified" = true
              AND u."subtype" = 'PLATFORM_AND_CFM'
              AND ck."kyc_verified" = true
              AND c."name" NOT LIKE 'xxxx%'
              AND EXISTS (SELECT 1
                          FROM "CreditAssessment" cat
                          WHERE cat."company_id" = u."CompanyId")
            ORDER BY c."name" DESC
            LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/getCompanyData.json', result1.rows);
      });
    });

    it('Get Jewellery SKU', () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `select sku from "Jewellery" j WHERE j."status" = 'CREATED' order by j."createdAt" desc LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/getJewellerySKU.json', result1.rows);
      });
    });

    it('Get Unverified Supplier', () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email"
              FROM "Users" u
                      INNER JOIN "Companies" c ON c.id = u."CompanyId"
              WHERE u."role" = 'SUPPLIER'
                AND u."verifyStatus" = '2'
              ORDER BY c."createdAt" DESC
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/nonVerifiedSupplier.json', result1.rows);
      });
    });
    it('Checkout To Nivoda Cart User', () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email", cs."CompanyId" , ("firstName" || ' ' || "lastName") AS name
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 inner join "CompanyPreferences" cp on cp."CompanyId" = cs."CompanyId"
        WHERE u."role" = 'CUSTOMER'
          AND cs."accept_holds" = true
          AND u."verifyStatus" = '4'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."LocationId" IS NOT NULL
          AND u.geo_country is not null
          and cp.checkout_from_nivoda_cart = true
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/getcheckouttonivodacartuser.json', result1.rows);
      });
    });
    it('Checkout To Showroom Cart User', () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: `SELECT u."email" AS "email", cs."CompanyId" , ("firstName" || ' ' || "lastName") AS name
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 inner join "CompanyPreferences" cp on cp."CompanyId" = cs."CompanyId"
        WHERE u."role" = 'CUSTOMER'
          AND cs."accept_holds" = true
          AND u."verifyStatus" = '4'
          AND u."status" = '4'
          AND u."address_verified" = 'true'
          AND u."LocationId" IS NOT NULL
          AND u.geo_country is not null
          and cp.checkout_from_nivoda_cart = false
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/checkouttoshowroomcartuser.json', result1.rows);
      });
    });
    it('Get Out Of Stock Stone For Showroom', () => {
      cy.task('READFROMDB', {
        dbConfig: Cypress.config('DB'),
        sql: ` SELECT nc."certNumber"
        FROM "Users" u
                 INNER JOIN "CompanySettings" cs ON u."CompanyId" = cs."CompanyId"
                 INNER JOIN "Diamonds" d ON cs."CompanyId" = d."CompanyId"
                 INNER JOIN "NaturalCertificates" nc ON d."CertificateId" = nc."id"
                 LEFT JOIN "Qcs" qc ON nc.id = qc."CertificateId"
                 LEFT JOIN (SELECT "OfferId", substring("OfferId" from position('/' in "OfferId") + 1)::uuid AS "DiamondId"
                            FROM "CartItems") si ON d."id" = si."DiamondId"
        WHERE u."role" = 'SUPPLIER'
          AND d."OrderItemId" IS not NULL
          AND d."CertificateType" = 'NaturalCertificate'
          AND u."default_currency" = 'USD'
          AND cs."cert_details_enabled" = true
          AND u."verifyStatus" = '4'
          AND si."OfferId" IS NULL
          AND d.brown = 0
          AND d.green = 0
          AND d.milky = 0
          AND d."eyeClean" = 100
          AND d."valueWithDiscount" < 100
          AND nc.verified = 'true'
          AND cs.accept_returns = true
          AND qc."CertificateId" IS null
              LIMIT 1;`
      }).then((result1) => {
        cy.writeFile('cypress/fixtures/OutOfStockShowroomStone.json', result1.rows);
      });
    });
  }
);
it('Get ShowroomUsers', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `
      SELECT 
        u.email,
        c.name,
        c.id,
        c.country_of_incorporation,
        c.status
      FROM "Companies" c
      INNER JOIN "Users" u ON c.id = u."CompanyId"
      WHERE 
        c."type" = 'CUSTOMER'
        AND c.status = 2
        AND u.status = 4
        AND c.country_of_incorporation = 'US'
        AND c.id = (
          SELECT c2.id
          FROM "Companies" c2
          JOIN "Users" u2 ON c2.id = u2."CompanyId"
          WHERE 
            c2."type" = 'CUSTOMER'
            AND c2.status = 2
            AND u2.status = 4
            AND c2.country_of_incorporation = 'US'
          GROUP BY c2.id
          HAVING COUNT(u2.id) > 1
          LIMIT 1
        );
    `
  }).then((result) => {
    if (result.rows.length >= 2) {
      cy.writeFile('cypress/fixtures/showroomuser1.json', {
        email: result.rows[0].email
      });
      cy.writeFile('cypress/fixtures/showroomuser2.json', {
        email: result.rows[1].email
      });
    } else {
      throw new Error('Less than 2 users returned in the result.');
    }
  });
});

it('Get Nivoda Curated Stone', { tags: '@nivodacurated' }, () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT * from "Gemstones" g
              inner join "GemCertificates" gc on gc.id = g."CertificateId"
              WHERE "nivoda_curated" = true
              AND g.availability = 'AVAILABLE' and g."OrderItemId" is null;`
  }).then((result) => {
    if (result.rows && result.rows.length > 0) {
      cy.writeFile('cypress/fixtures/getNivodaCuratedStone.json', result.rows);
    } else {
      cy.log('No data returned for Nivoda curated stones');
    }
  });
});

it('Get Jewellery SKU excluding Jewellery Request', () => {
  cy.task('READFROMDB', {
    dbConfig: Cypress.config('DB'),
    sql: `SELECT sku FROM "Jewellery" j WHERE j."status" = 'CREATED' AND j."sku" NOT LIKE 'JR-%' ORDER BY j."createdAt" DESC LIMIT 1;`
  }).then((result1) => {
    cy.writeFile('cypress/fixtures/getJewellerySKUexcludingJR.json', result1.rows);
  });
});
