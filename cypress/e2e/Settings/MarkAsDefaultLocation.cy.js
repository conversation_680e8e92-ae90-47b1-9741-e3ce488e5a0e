import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('MarkAsDefaultLocation', { tags: ["@Customer-Settings", "@Regression"] }, () => {
  it('Mark As Default Location (NE-TC-155)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.visitShippingDestination();
    settings.markAsDefaultLocation('Default location updated successfully.');
  });
});
