import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('VisitComapnySettings', { tags: ["@Customer-Settings", "@Regression"] }, () => {
  it('Visit Company Settings (NE-TC-142)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.openCompanySetting();
  });
});
