import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('UpdateUser', { tags: ["@Customer-Settings", "@Regression"] }, () => {
  it('Update User (NE-TC-151)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.openUserManagment();
    settings.updateUser('companyowner.json', 'User edited successfully');
  });
});
