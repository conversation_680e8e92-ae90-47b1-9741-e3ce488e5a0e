import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('EnableOrderShippingUpdatesEmaiLNotification', { tags: ["@Customer-Settings", "@Regression"] }, () => {
  it('Verify Order Shipping Updates Notification is Enabled (NE-TC-1213)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.changeEmailNotifications('Order shipping updates', 'enableEmail', 'Notification settings are saved');
  });
});
