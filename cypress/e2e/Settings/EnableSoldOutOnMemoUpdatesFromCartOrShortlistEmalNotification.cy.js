import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe(
  'EnableSoldOut/OnMemoUpdatesFromCartOrShortlistEmaiLNotification',
  { tags: ["@Customer-Settings", "@Regression"] },
  () => {
    it('Verify Sold out/on memo updates from cart or shortlist Notification is Enabled (NE-TC-1231)', () => {
      const login = new Login();
      const menu = new Menu();
      const settings = new Settings();

      login.loginUsingApi('companyowner.json');
      menu.visitSettingsPage();
      settings.changeEmailNotifications(
        'Sold out/on memo updates from cart or shortlist',
        'enableEmail',
        'Notification settings are saved'
      );
    });
  }
);
