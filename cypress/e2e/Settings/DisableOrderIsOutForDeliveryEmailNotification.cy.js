import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('DisableOrderIsOutForDeliveryEmaiLNotification', { tags: ["@Customer-Settings", "@Regression"] }, () => {
  it('Verify Order Is Out For Delivery (last mile) Notification is Disabled (NE-TC-1223)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.changeEmailNotifications(
      'Order is out for delivery (last mile)',
      'disableEmail',
      'Notification settings are saved'
    );
  });
});
