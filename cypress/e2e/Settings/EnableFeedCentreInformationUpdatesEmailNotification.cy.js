import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe(
  'EnableFeedCentre-InformationUpdatesEmaiLNotification',
  { tags: ["@Customer-Settings", "@Regression"] },
  () => {
    it('Verify Feed Centre - Information Updates Notification is Enabled (NE-TC-1234)', () => {
      const login = new Login();
      const menu = new Menu();
      const settings = new Settings();

      login.loginUsingApi('companyowner.json');
      menu.visitSettingsPage();
      settings.changeEmailNotifications(
        'Feed centre - information updates',
        'enableEmail',
        'Notification settings are saved'
      );
    });
  }
);
