import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('EnableRequest-InformationUpdatesEmailNotification', { tags: ["@Customer-Settings", "@Regression"] }, () => {
  it('Verify Request - Information Updates is Enabled (NE-TC-1236)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.changeEmailNotifications(
      'Request - information updates',
      'enableEmail',
      'Notification settings are saved'
    );
  });
});
