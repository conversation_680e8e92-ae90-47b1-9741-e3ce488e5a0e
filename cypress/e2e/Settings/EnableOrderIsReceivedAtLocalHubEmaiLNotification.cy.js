import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('EnaableOrderIsReceivedAtLocalHubEmaiLNotification', { tags: ["@Customer-Settings", "@Regression"] }, () => {
  it('Verify Order Is Received At Local Hub Notification is Enabled (NE-TC-1214)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.changeEmailNotifications(
      'Order is received at local hub',
      'enableEmail',
      'Notification settings are saved'
    );
  });
});
