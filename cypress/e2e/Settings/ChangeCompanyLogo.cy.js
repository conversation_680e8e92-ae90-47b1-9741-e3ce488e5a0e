import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';
describe('ChangeCompanyLogo', { tags: ["@Customer-Settings", "@Regression"] }, () => {
  it('Change Company Logo (NE-TC-143)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.openCompanySetting();
    settings.updateComapnyLogo('Successfully updated your company logo.');
  });
});
