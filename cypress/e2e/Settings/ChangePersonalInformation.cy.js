import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('ChangePersonalInformation', { tags: ["@Customer-Settings", "@Regression"] }, () => {
  it('Change Personal Information (NE-TC-140)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.updatePersonalSettings('companyowner.json', 'Successfully updated your profile.');
  });
});
