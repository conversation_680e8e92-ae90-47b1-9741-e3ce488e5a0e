import Login from '../../support/Login';
import Menu from '../../support/Menu';
import Settings from '../../support/Settings';

describe('SearchDestination', { tags: ["@Customer-Settings", "@Regression"] }, () => {
  it('Search Destination (NE-TC-156)', () => {
    const login = new Login();
    const menu = new Menu();
    const settings = new Settings();

    login.loginUsingApi('companyowner.json');
    menu.visitSettingsPage();
    settings.visitShippingDestination();
    settings.searchDestination();
  });
});
