import Login from '../../support/Login';
import Product from '../../support/Product';
import Filter from '../../support/Filter';

describe('Save Search Function', { tags: ["@Regression", "@CX", "@Failed"] }, () => {
  it('user should save the search (NE-TC-2434)', () => {
    const login = new Login();
    const product = new Product();
    const filter = new Filter();

    login.loginUsingApi('morcustomer.json');

    filter.selectAllFilters();
    filter.selectFancyColor();
    filter.applyDiamondFilters();

    product.savedSearch();
  });
});
