import Login from '../../support/Login';
import Settings from '../../support/Settings';

describe('Verify Language dropdown', { tags: ["@Regression", "@CX"] }, () => {
  const login = new Login();
  const settings = new Settings();

  beforeEach(() => {
    login.visitPage();
    login.loginUsingUi('morcustomer.json');
  });

  it.only(`Should display correct translation for English (NE-TC-3260)`, () => {
    settings.updateLanguageandVerify('English', 'Natural diamonds');
  });

  it(`Should display correct translation for French (NE-TC-3261)`, () => {
    settings.updateLanguageandVerify('Français', 'Diamants naturels');
  });

  it(`Should display correct translation for Italiano (NE-TC-3262)`, () => {
    settings.updateLanguageandVerify('Italiano', 'Diamanti naturali');
  });

  it(`Should display correct translation for Español (NE-TC-3263)`, () => {
    settings.updateLanguageandVerify('Español', 'Diamantes naturales');
  });

  it(`Should display correct translation for Deutsch (NE-TC-3264)`, () => {
    settings.updateLanguageandVerify('Deutsch', 'Natürliche diamanten');
  });

  it(`Should display correct translation for Japanese (NE-TC-3265)`, () => {
    settings.updateLanguageandVerify('Japanese', '天然ダイヤモンド');
  });
});
