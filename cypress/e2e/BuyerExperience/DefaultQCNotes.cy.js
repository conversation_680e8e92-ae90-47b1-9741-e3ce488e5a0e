import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('DefaultQCNotes', { tags: ['@Regression', '@CX'] }, () => {
  it('user should be able to view Default QC Requirements (NE-TC-2470)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    login.loginUsingApi('morcustomer.json');
    navbar.visitGemStones();
    product.addProductToCart('gemstonecert.json');
    product.defaultQCNotes();
  });
});
