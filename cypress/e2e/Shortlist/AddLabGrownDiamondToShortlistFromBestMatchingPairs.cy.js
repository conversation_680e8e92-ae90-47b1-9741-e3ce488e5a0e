import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('AddLabGrownDiamondToShortlistFromBestMatchingPairs', { tags: ["@Shortlist", "@Regression", "@CX"] }, () => {
  it('Verify User Can Add Lab Grown Diamond To Shortlist From Best Matching Pairs (NE-TC-665)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getshortlistlabdiamondpair();

    login.loginUsingApi('morcustomer.json');
    navbar.visitLabgrownDiamonds();
    product.addToShortlistFromPairDetails('shortlistlabdiamondpair.json');
  });
});
