import Login from '../../support/Login';
import Shortlist from '../../support/Shortlist';
import Menu from '../../support/Menu';
import Product from '../../support/Product';

describe('ExportShortlist', { tags: ["@Shortlist", "@Regression", "@CX"] }, () => {
  it('Verify User Can Export A Shortlist (NE-TC-654)', () => {
    const login = new Login();
    const shortlist = new Shortlist();
    const menu = new Menu();
    const product = new Product();

    login.loginUsingApi('morcustomer.json');
    product.shortlistButtonVerify('diamondshortlistcerts1.json');
    menu.visitMenu();
    menu.visitShortList();
    shortlist.exportShortlist('Diamonds');
  });
});
