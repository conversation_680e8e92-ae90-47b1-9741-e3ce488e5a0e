import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('AddLabGrownDiamondToShortlistFromMatchingPairsPage', { tags: ["@Shortlist", "@Regression", "@CX"] }, () => {
  it('Verify User Can Add Lab Grown Diamond To Shortlist From Matching Pairs Page (NE-TC-664)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getshortlistlabdiamond1();
    login.loginUsingApi('morcustomer.json');
    navbar.visitLabgrownDiamonds();
    product.addToShortlistFromPairIcon('shortlistlabdiamond1.json');
  });
});
