import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Product from '../../support/Product';

describe('PDP View (NE-TC-2415)', { tags: ['@Regression', '@CX'] }, () => {
  const login = new Login();
  const product = new Product();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingApi('morcustomer.json');
  });

  it('Verify product description page for natural diamonds', () => {
    navBar.visitNaturalDiamonds();
    product.pdpView();
  });

  it('Verify product description page for labgrown diamonds', () => {
    navBar.visitLabgrownDiamonds();
    product.pdpView();
  });

  it('Verify product description page for gemstones', () => {
    navBar.visitGemStones();
    product.clickOnGemstone('Sapphire blue oval 0.94ct');
  });
});
