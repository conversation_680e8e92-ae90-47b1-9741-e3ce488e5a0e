import Login from '../../support/Login';
import Navbar from '../../support/Navbar';
import Product from '../../support/Product';

describe('VerifyStockIdNotVisibleForUnverifiedUsersOnGridView', { tags: ["@Product-PLP", "@Regression"] }, () => {
  const login = new Login();
  const product = new Product();
  const navBar = new Navbar();

  beforeEach(() => {
    login.loginUsingApi('nonverifiedusers.json');
  });

  it('Verify Stock Id Is Not Visible For Natural Diamonds in PLP Grid View For Unverified Users (NE-TC-2107)', () => {
    product.stockIdNotVisibleOnStoneCard();
  });

  it('Verify Stock Id Is Not Visible For Lab Grown Diamonds in PLP Grid View Unverified Users (NE-TC-2110)', () => {
    navBar.visitLabgrownDiamonds();
    product.stockIdNotVisibleOnStoneCard();
  });

  it.skip('Verify Stock Id Is Not Visible For Gemstones in PLP Grid View Unverified Users (NE-TC-2104)', () => {
    navBar.visitGemStones();
    product.stockIdNotVisibleOnStoneCard();
  });
});
