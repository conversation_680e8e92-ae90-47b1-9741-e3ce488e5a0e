import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('AccessPairsListFromNaturalDiamondsDetailsPage', { tags: ["@Product-PLP", "@Regression"] }, () => {
  it('Verify User Can Access Pairs List For Natural Diamonds Details Page (NE-TC-659)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getnaturaldiamondcert();
    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalDiamonds();
    product.accessPairListFromDetails('naturaldiamondcert.json');
  });
});
