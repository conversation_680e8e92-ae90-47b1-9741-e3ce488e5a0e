import Login from '../../support/Login';
import Product from '../../support/Product';
import Navbar from '../../support/Navbar';

describe('AccessPairsListFromNaturalDiamondsPLP', { tags: ["@Product-PLP", "@Regression"] }, () => {
  it('Verify User Can Access Pairs List For Natural Diamonds (NE-TC-656)', () => {
    const login = new Login();
    const product = new Product();
    const navbar = new Navbar();

    cy.getnaturaldiamondcert();
    login.loginUsingApi('morcustomer.json');
    navbar.visitNaturalDiamonds();
    product.accessPairListFromIcon('naturaldiamondcert.json');
  });
});
