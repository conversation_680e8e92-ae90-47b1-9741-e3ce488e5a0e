import Login from '../../support/Login';
import Product from '../../support/Product';
import Checkout from '../../support/Checkout';
import AccessAdminTabs from '../../support/Admin/AccessAdminTabs';
import Shipment from '../../support/Admin/Shipments';
import Admin from '../../support/Admin';
import Returns from '../../support/Admin/Returns';
import Accounting from '../../support/Admin/Accounting';
import Menu from '../../support/Menu';
import FinanceDashboard from '../../support/FinanceDashboard';

describe('CreateSbsShipment', { tags: ["@SbsShipments", "@OTP", "@Failed"] }, () => {
  const login = new Login();
  const product = new Product();
  const checkout = new Checkout();
  const accessAdminTabs = new AccessAdminTabs();
  const shipment = new Shipment();
  const admin = new Admin();
  const returns = new Returns();
  const accounting = new Accounting();

  it('Verify User Can Place A Normal Order (NE-TC-2598)', () => {
    cy.getcreditnotestone();
    cy.getMultipleAddressUser();

    login.loginUsingApi('multipleaddressuser.json');
    product.addProductToCart('cypress/fixtures/creditnotestone.json');
    product.viewCart();
    checkout.proceedThroughCheckout();
    checkout.proceedToPlaceOrder();
    checkout.placeOrder();
    checkout.orderConfirmationText();
  });

  it('Verify Admin Can Confirm A Normal Order (NE-TC-2603) (NE-TC-2600)', () => {
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Orders', 'Purchased', 'Purchase order', 'orders/purchase-order');
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/creditnotestone.json',
      'cypress/fixtures/orderNumber.json',
      'Confirm + RTC (1)',
      'Status update successful for 1 item(s) and moved to Confirm + RTC tab'
    );
  });

  it('Verify Admin Can Confirm QC Pass and create SBS interoffice shipment IN-US (NE-TC-2616)', () => {
    cy.clearAllSessionStorage();
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessTabs('Orders', 'Purchased', 'Purchase order', 'orders/purchase-order');
    admin.accessRtcTab();
    admin.confirmRTC(
      'Diamond',
      'cypress/fixtures/creditnotestone.json',
      'cypress/fixtures/orderNumber.json',
      'Collected',
      'Status update successful for 1 item(s).'
    );
    cy.wait(4000);
    admin.markStoneQcPass('orderNumber.json');
    accessAdminTabs.accessTabs('Shipments', 'Create shipment', '', 'create/customer-shipments');
    shipment.createShipmentAndVerifyAddressSbs(
      'BDB Mumbai Office IN',
      'NYC Office US',
      'cypress/fixtures/multipleaddressuser.json',
      'orderNumber.json'
    );
  });

  it('Verify An Admin can create SBS LM forward shipment from US hub to a US customer (NE-TC-2612)', () => {
    cy.clearAllSessionStorage();
    login.loginUsingAdminApi('loginasadmin.json');
    accessAdminTabs.accessLocationShipments('New York', 'US', 'office-incoming', 'office-orders/US/office-incoming');
    shipment.receiveShipmentByTrackingCode();
    accessAdminTabs.accessTabs('New York', 'SBS', '', 'US/sbs');
    shipment.createSbsLMShipment();
  });
});
