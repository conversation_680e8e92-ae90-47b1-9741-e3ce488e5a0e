import Kyc from '../../support/Admin/Kyc.js';
import Login from '../../support/Login.js';

describe('Verifying Unverified KYC Customers', { tags: ["@Failed", "@Regression"]}, () => {
  it('Verify KYC for Unverified Customer NE-TC-2388', () => {
    const login = new Login();
    const kyc = new Kyc();

    login.loginUsingAdminApi('loginasadmin.json');

    kyc.accessKycPendingCompanyDashboard();
    kyc.remainInSameKycTab();
    kyc.accessKycDetailsDashboard();
    kyc.fillBusinessKycDetails('kyc-upload.pdf');
    kyc.fillIndividualDetails('kyc-upload.pdf');
    kyc.fillUboDetails();
    kyc.performKycAssessment();
  });
});
