import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('AccessFindThePerfectStoneWizard', { tags: ['@Showroom'] }, () => {
  it('Verify user is able to click on find the perfect stone that directs to wizard (NE-TC-3205)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();

    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroom.accessPerfectStoneWizard();
  });
});
