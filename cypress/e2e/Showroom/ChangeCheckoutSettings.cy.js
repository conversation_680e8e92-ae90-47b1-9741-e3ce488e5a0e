import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('Access&ChangeCheckoutOption', { tags: ['@Showroom'] }, () => {
  it('Access Checkout Setting & Verify Confirmation Popup When Switching to New Ordering Experience on Showroom  (ST-TC-28)(ST-TC-29)(ST-TC-30)(ST-TC-31)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();

    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroomLogin.accessSettings('Checkout settings', '/ordering-and-experience/checkout-settings');
    showroom.changeCheckoutSetting();
  });
});
