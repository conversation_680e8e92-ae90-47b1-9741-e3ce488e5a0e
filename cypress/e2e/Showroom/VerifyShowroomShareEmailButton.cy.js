import Showroom from '../../support/Showroom/Showroom';
import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import ShowroomSharingOptions from '../../support/Showroom/ShowroomSharingOptions';
describe('VerifyWhatsAppShareButton', { tags: ['@Sharing-Options', '@Regression', '@Showroom'] }, () => {
  const showroomLogin = new ShowroomLogin();
  const showroomSharingOptions = new ShowroomSharingOptions();
  const showroom = new Showroom();

  beforeEach(() => {
    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
  });

  it('Verify Share Email Button Is Visible For Natural Diamonds For PLP (NE-TC-3255)', () => {
    showroomSharingOptions.verifyEmailSharingOption();
  });

  it('Verify Share Whatsapp Button Is Visible For Lab Grown Diamonds For PLP (NE-TC-3256)', () => {
    showroom.visitLabGrownDiamonds();
    showroomSharingOptions.verifyEmailSharingOption();
  });
  it('Verify Share Whatsapp Button Is Visible For Natural Diamonds For PDP (NE-TC-3258)', () => {
    cy.getnaturaldiamondcert();
    showroom.searchStone('naturaldiamondcert.json');
    showroom.verifyDiamondText('Natural diamonds', 'Natural diamonds (1)');
    showroomSharingOptions.verifyEmailSharingOptionInProduct();
  });
  it('Verify Whatsapp Share Button Is Visible For Lab Grown Diamonds For PDP (NE-TC-3259)', () => {
    cy.getlabdiamondcert();
    showroom.visitLabGrownDiamonds();
    showroom.searchStone('labdiamondcert.json');
    showroom.verifyDiamondText('Lab grown diamonds', 'Lab grown diamonds (1)');
    showroomSharingOptions.verifyEmailSharingOptionInProduct();
  });
});
