import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('VerifyBasketCountOnCheckoutSettingChange.cy.js', { tags: ['@Showroom'] }, () => {
  it('Verify Cart is Cleared and Switch is Applied When Switching to New Experience on Showroom or old experience (ST-TC-32)(ST-TC-44)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();

    cy.getnaturaldiamondcert();

    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroom.accessDashboard();
    showroom.addProductToCart('naturaldiamondcert.json', 'Natural diamonds', 'Natural diamonds (1)');
    showroom.verifyBasketCount();
    showroomLogin.accessSettings('Checkout settings', '/ordering-and-experience/checkout-settings');
    showroom.changeCheckoutSetting();
    showroom.verifyBasketCountOnCheckoutChange();
  });
});
