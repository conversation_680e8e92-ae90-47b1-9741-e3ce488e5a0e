import ShowroomLogin from '../../support/Showroom/ShowroomLogin';
import Showroom from '../../support/Showroom/Showroom';

describe('accessDashboard', { tags: ['@Regression', '@Showroom'] }, () => {
  it('Verify user is able to click on browse selection that directs to browse page (NE-TC-3204)', () => {
    const showroomLogin = new ShowroomLogin();
    const showroom = new Showroom();

    showroomLogin.visitPage();
    showroomLogin.loginUsingUi('getcheckouttonivodacartuser.json');
    showroomLogin.loginAsCustomerAssertion();
    showroom.accessDashboard();
  });
});
